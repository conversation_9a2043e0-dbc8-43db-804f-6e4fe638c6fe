{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397697911329786", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397697905342202", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 30837}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 40071}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "network_stats": {"srtt": 33823}, "server": "https://accounts.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 41149}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ssl.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 31120}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397704025806881", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3VwZGF0ZS5nb29nbGVhcGlzLmNvbQAAAA==", false, 0], "network_stats": {"srtt": 32512}, "server": "https://update.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397704296812238", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 32988}, "server": "https://clients2.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 34536}, "server": "https://rr2---sn-vgqsrnlz.c.youtube.com"}, {"anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 31051}, "server": "https://rr4---sn-vgqskn66.c.youtube.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397697904476628", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397697904265327", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705120917631", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 33327}, "server": "https://www.youtube.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 31003}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 68698}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 46559}, "server": "https://lh3.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 35298}, "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 32161}, "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705112677263", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 33217}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705112875202", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 42242}, "server": "https://history.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705113367145", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 32071}, "server": "https://i9.ytimg.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705111678138", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 35648}, "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705113307142", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 34233}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705112846784", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 34233}, "server": "https://ssl.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397703748237037", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 33230}, "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705121411336", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 39752}, "server": "https://realtimesupport.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705121447309", "port": 443, "protocol_str": "quic"}], "anonymization": ["IAAAABoAAABodHRwczovL3d3dy5nb29nbGVhcGlzLmNvbQAA", false, 0], "network_stats": {"srtt": 36654}, "server": "https://www.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705121460484", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 29574}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705121638315", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 63591}, "server": "https://casespartner-pa.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705158263388", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 37287}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397705136378034", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 31640}, "server": "https://upload.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "server": "https://rr5---sn-vgqsrn6l.c.youtube.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 39980}, "server": "https://accounts.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 31362}, "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 33327}, "server": "https://studio.youtube.com", "supports_spdy": true}], "supports_quic": {"address": "*************", "used_quic": true}, "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G"}}}
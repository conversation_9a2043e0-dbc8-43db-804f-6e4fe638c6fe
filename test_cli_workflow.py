#!/usr/bin/env python3
"""
Test script for CLI workflow functionality

This script tests the new CLI features including video tracking,
statistics, and command-line argument parsing.
"""

import sys
import os
import json
import subprocess
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from Helpers.VideoTracker import VideoTracker

def test_video_tracker():
    """Test the video tracker functionality"""
    print("🧪 Testing Video Tracker...")
    
    # Create test tracker
    test_file = "test_video_history.json"
    tracker = VideoTracker(test_file)
    
    # Add some test videos
    tracker.add_video(
        title="Market Analysis: Tech Stocks Surge Today",
        topic="Technology Stock Market Analysis",
        video_id="ABC123",
        video_url="https://youtube.com/watch?v=ABC123",
        upload_status="uploaded",
        tags=["tech", "stocks", "market"],
        description_length=250,
        video_duration=45.5
    )
    
    tracker.add_video(
        title="Crypto Update: Bitcoin Breaks New Highs",
        topic="Cryptocurrency Market Update",
        video_id="DEF456",
        video_url="https://youtube.com/watch?v=DEF456",
        upload_status="uploaded",
        tags=["crypto", "bitcoin", "market"],
        description_length=180,
        video_duration=38.2
    )
    
    tracker.add_video(
        title="Weekly Market Wrap-Up",
        topic="Weekly Financial Summary",
        upload_status="pending",
        tags=["weekly", "summary", "market"],
        description_length=200
    )
    
    # Test duplicate detection
    print(f"✅ Duplicate topic test: {tracker.is_duplicate_topic('Technology Stock Analysis')}")
    print(f"✅ Duplicate title test: {tracker.is_duplicate_title('Market Analysis: Tech Stocks Rise')}")
    
    # Get statistics
    stats = tracker.get_statistics()
    print(f"✅ Statistics: {stats}")
    
    # Get today's topics
    todays_topics = tracker.get_todays_topics()
    print(f"✅ Today's topics: {todays_topics}")
    
    # Clean up
    Path(test_file).unlink(missing_ok=True)
    print("✅ Video tracker test completed")
    
    return True

def test_cli_commands():
    """Test CLI command functionality"""
    print("\n🧪 Testing CLI Commands...")
    
    # Create test data
    test_file = "test_cli_history.json"
    tracker = VideoTracker(test_file)
    
    # Add test data
    tracker.add_video(
        title="Test Video 1",
        topic="Test Topic 1",
        video_id="TEST123",
        video_url="https://youtube.com/watch?v=TEST123",
        upload_status="uploaded"
    )
    
    tracker.add_video(
        title="Test Video 2", 
        topic="Test Topic 2",
        upload_status="pending"
    )
    
    try:
        # Test --stats command
        print("📊 Testing --stats command...")
        result = subprocess.run([
            sys.executable, "workflow.py", 
            "--stats", 
            "--storage-file", test_file
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ --stats command successful")
        else:
            print(f"❌ --stats command failed: {result.stderr}")
        
        # Test --list command
        print("📋 Testing --list command...")
        result = subprocess.run([
            sys.executable, "workflow.py",
            "--list",
            "--days", "7",
            "--storage-file", test_file
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ --list command successful")
        else:
            print(f"❌ --list command failed: {result.stderr}")
        
        # Test help (no arguments)
        print("❓ Testing help display...")
        result = subprocess.run([
            sys.executable, "workflow.py"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Help display successful")
        else:
            print(f"❌ Help display failed: {result.stderr}")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ CLI command test timed out")
        return False
    except Exception as e:
        print(f"❌ CLI command test error: {e}")
        return False
    finally:
        # Clean up
        Path(test_file).unlink(missing_ok=True)

def test_cron_script():
    """Test the cron reset script"""
    print("\n🧪 Testing Cron Reset Script...")
    
    # Create test data
    test_file = "test_cron_history.json"
    tracker = VideoTracker(test_file)
    
    # Add test data
    tracker.add_video("Test Video", "Test Topic", upload_status="uploaded")
    
    try:
        # Test cron reset
        result = subprocess.run([
            sys.executable, "cron_reset.py"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Cron reset script successful")
            
            # Check if backup was created
            backup_files = list(Path(".").glob("video_history_backup_*.json"))
            if backup_files:
                print(f"✅ Backup file created: {backup_files[-1]}")
                # Clean up backup
                backup_files[-1].unlink()
            
            return True
        else:
            print(f"❌ Cron reset script failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Cron reset test timed out")
        return False
    except Exception as e:
        print(f"❌ Cron reset test error: {e}")
        return False
    finally:
        # Clean up
        Path(test_file).unlink(missing_ok=True)

def test_json_persistence():
    """Test JSON file persistence"""
    print("\n🧪 Testing JSON Persistence...")
    
    test_file = "test_persistence.json"
    
    # Create tracker and add data
    tracker1 = VideoTracker(test_file)
    tracker1.add_video("Persistence Test", "Test Topic")
    
    # Create new tracker instance and verify data persists
    tracker2 = VideoTracker(test_file)
    
    if len(tracker2.videos) == 1 and tracker2.videos[0].title == "Persistence Test":
        print("✅ JSON persistence working correctly")
        result = True
    else:
        print("❌ JSON persistence failed")
        result = False
    
    # Clean up
    Path(test_file).unlink(missing_ok=True)
    
    return result

def main():
    """Run all CLI tests"""
    print("🚀 YTFinance CLI Testing")
    print("=" * 50)
    
    tests = [
        ("Video Tracker", test_video_tracker),
        ("CLI Commands", test_cli_commands),
        ("Cron Script", test_cron_script),
        ("JSON Persistence", test_json_persistence)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} test...")
            if test_func():
                passed += 1
                print(f"✅ {test_name} test passed")
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test error: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All CLI tests passed!")
        print("\n💡 Ready to use:")
        print("   python workflow.py --run              # Create new video")
        print("   python workflow.py --stats            # Show statistics")
        print("   python workflow.py --list             # List recent videos")
        print("   python workflow.py --reset            # Reset history")
        print("   python cron_reset.py                  # Cron reset script")
        return True
    else:
        print("💥 Some CLI tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

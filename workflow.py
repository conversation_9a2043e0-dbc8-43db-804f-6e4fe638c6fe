from Helpers.GoogleTrends import GoogleTrendsClient
from Helpers.DuckDuckGoSearch import DuckDuckGoSearchClient, SafeSearch
from Helpers.WebScraper import WebScraperClient
from Helpers.GeminiImageGen import Gemini<PERSON>mageGen, GeminiImageGenError, ImageAspectRatio
from Helpers.KokoroTTS import <PERSON>koroTTS, KokoroTTSError, create_tts_client, LanguageCode
from Helpers.VideoAssembler import VideoAssembler, VideoAssemblerError, VideoConfig
from Helpers.SubtitleOverlay import SubtitleOverlay, SubtitleOverlayError, SubtitleTiming

from Agents.StoryPicker import StoryPickerAgent
from Agents.SearchTerms import SearchTermAgent
from Agents.DataExtractor import ExtractorAgent
from Agents.ScriptWriter import ScriptWriter, ScriptParserError
from Agents.YouTubeMetadata import YouTubeMetadataAgent, YouTubeMetadataError
from Helpers.RetryMechanism import create_image_retry_mechanism, create_llm_retry_mechanism, RetryError
from Helpers.YouTubeUploader import <PERSON><PERSON><PERSON>loader, UploadConfig, YouTubeUploaderError
from Helpers.VideoTracker import VideoTracker, VideoRecord

import time
import os
import shutil
import json
import argparse
import sys
import logging
import random
from pathlib import Path
from datetime import datetime

# Rich imports for beautiful console output
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn, TimeElapsedColumn
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.layout import Layout
from rich.live import Live
from rich import box
from rich.align import Align
from rich.columns import Columns
from rich.tree import Tree
from rich.status import Status

# Initialize rich console
console = Console()

# Global variables
# (todays_used_topics now handled by VideoTracker)

def display_header():
    """Display a beautiful header for the application"""
    header_text = Text()
    header_text.append("🎬 ", style="bold yellow")
    header_text.append("YouTube Shorts", style="bold cyan")
    header_text.append(" AI Generator", style="bold magenta")

    subtitle = Text("Automated content creation pipeline with trending topics", style="italic bright_blue")

    header_panel = Panel(
        Align.center(
            Text.assemble(
                header_text, "\n",
                subtitle
            )
        ),
        box=box.DOUBLE,
        border_style="bright_cyan",
        padding=(1, 2)
    )

    console.print()
    console.print(header_panel)
    console.print()

def create_stats_table(stats):
    """Create a beautiful stats table"""
    table = Table(title="📊 Session Statistics", box=box.ROUNDED, border_style="cyan")

    table.add_column("Metric", style="bold white", no_wrap=True)
    table.add_column("Value", style="bold green", justify="right")

    for key, value in stats.items():
        table.add_row(key, str(value))

    return table

def display_topic_processing(topic):
    """Display topic processing header"""
    topic_panel = Panel(
        Align.center(
            Text(f"🎯 Processing Topic: {topic}", style="bold yellow")
        ),
        box=box.HEAVY,
        border_style="yellow",
        padding=(0, 2)
    )
    console.print(topic_panel)

# Display header
display_header()

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="YTFinance - Automated YouTube Finance Video Creator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python workflow.py --run              # Create and upload a new video
  python workflow.py --reset            # Reset video history (for cron)
  python workflow.py --stats            # Show video statistics
  python workflow.py --list             # List recent videos
  python workflow.py --run --no-upload  # Create video without uploading
  python workflow.py --upload           # Upload current video in temp directory
        """
    )

    # Main commands
    parser.add_argument(
        "--run",
        action="store_true",
        help="Run the video creation workflow"
    )

    parser.add_argument(
        "--reset",
        action="store_true",
        help="Reset video history (useful for scheduled cleanup)"
    )

    parser.add_argument(
        "--stats",
        action="store_true",
        help="Show video creation statistics"
    )

    parser.add_argument(
        "--list",
        action="store_true",
        help="List recent videos"
    )

    parser.add_argument(
        "--upload",
        action="store_true",
        help="Upload the current video in temp directory"
    )

    # Options
    parser.add_argument(
        "--no-upload",
        action="store_true",
        help="Create video but don't upload to YouTube"
    )

    parser.add_argument(
        "--days",
        type=int,
        default=7,
        help="Number of days for --list command (default: 7)"
    )

    parser.add_argument(
        "--storage-file",
        default="video_history.json",
        help="Path to video history storage file (default: video_history.json)"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging (shows all debug information)"
    )

    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress all output except errors (minimal output mode)"
    )

    return parser.parse_args()


def run_video_creation_workflow(video_tracker: VideoTracker, enable_upload: bool = True, quiet_mode: bool = False):
    """Run the main video creation workflow"""

    # Helper function for conditional console output
    def print_if_not_quiet(*args, **kwargs):
        if not quiet_mode:
            console.print(*args, **kwargs)

    # Session statistics
    session_stats = {
        "Topics Processed": 0,
        "Videos Created": 0,
        "Videos Uploaded": 0,
        "Total Runtime": "0:00:00",
        "Success Rate": "0%"
    }

    start_time = time.time()

    while True:
        try:
            # Update runtime
            elapsed = time.time() - start_time
            hours, remainder = divmod(elapsed, 3600)
            minutes, seconds = divmod(remainder, 60)
            session_stats["Total Runtime"] = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"

            # Calculate success rate
            if session_stats["Topics Processed"] > 0:
                success_rate = (session_stats["Videos Created"] / session_stats["Topics Processed"]) * 100
                session_stats["Success Rate"] = f"{success_rate:.1f}%"

            if not quiet_mode:
                with console.status("[bold green]🔍 Gathering trending searches...", spinner="dots"):
                    client = GoogleTrendsClient()
                    trends = client.get_trending_searches(pn='united_states', count=30, category='business_industrial')
            else:
                client = GoogleTrendsClient()
                trends = client.get_trending_searches(pn='united_states', count=30, category='business_industrial')

            print_if_not_quiet("✅ [bold green]Trending searches gathered successfully")

            # Get today's topics from video tracker to avoid duplicates
            todays_used_topics = video_tracker.get_todays_topics()

            if not quiet_mode:
                with console.status("[bold blue]🤖 Analyzing trends and picking story topics...", spinner="dots"):
                    story_topics = StoryPickerAgent(todays_used_topics, trends)
            else:
                story_topics = StoryPickerAgent(todays_used_topics, trends)

            if len(story_topics) == 0:
                print_if_not_quiet("⚠️  [bold yellow]No new story topics found, waiting 1 hour...")
                if not quiet_mode:
                    console.print(create_stats_table(session_stats))
                time.sleep(3600)
                continue

            print_if_not_quiet(f"✅ [bold green]Found {len(story_topics)} potential topics")

            # Select one random topic that's not a duplicate
            selected_topic = None

            # Shuffle topics to get random selection
            random.shuffle(story_topics)

            for topic in story_topics:
                # Check for duplicate topics
                if not video_tracker.is_duplicate_topic(topic, similarity_threshold=0.8):
                    selected_topic = topic
                    break
                else:
                    print_if_not_quiet(f"⚠️  [bold yellow]Skipping duplicate topic: {topic}")

            if selected_topic is None:
                print_if_not_quiet("⚠️  [bold yellow]All topics are duplicates, waiting 1 hour...")
                if not quiet_mode:
                    console.print(create_stats_table(session_stats))
                time.sleep(3600)
                continue

            # Process the selected topic
            topic = selected_topic
            session_stats["Topics Processed"] += 1

            print_if_not_quiet(f"🎯 [bold blue]Selected topic: {topic}")
            if not quiet_mode:
                display_topic_processing(topic)

            # Create progress tracking (only if not in quiet mode)
            if not quiet_mode:
                progress_context = Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TaskProgressColumn(),
                    TimeElapsedColumn(),
                    console=console,
                    transient=True
                )
            else:
                # Create a dummy progress object for quiet mode
                class DummyProgress:
                    def add_task(self, description, total=None):
                        return "dummy_task"
                    def update(self, task_id, description=None, **kwargs):
                        pass
                    def advance(self, task_id):
                        pass
                    def __enter__(self):
                        return self
                    def __exit__(self, *args):
                        pass

                progress_context = DummyProgress()

            with progress_context as progress:

                # Main pipeline tasks
                pipeline_task = progress.add_task("🎬 Video Creation Pipeline", total=7)

                # Step 1: Generate Search Terms
                progress.update(pipeline_task, description="🔍 Generating search terms...")
                search_terms = SearchTermAgent(topic)
                progress.advance(pipeline_task)
                print_if_not_quiet(f"✅ Generated {len(search_terms)} search terms")

                # Step 2: Gather Search Results
                progress.update(pipeline_task, description="🌐 Gathering search results...")
                gathered_results = []
                client = DuckDuckGoSearchClient(
                    max_results=5,
                    safe_search=SafeSearch.OFF,
                    enable_cache=True,
                    time_limit_hours=48  # Only get results from last 48 hours for up-to-date information
                )

                with WebScraperClient(headless=True, enable_cache=True) as scraper:
                    for term in search_terms:
                        response = client.search(term)
                        print_if_not_quiet(f"  📊 Found {response.total_results} results in {response.search_time:.2f}s for '{term}'")
                        for i, result in enumerate(response.results[:2], 1):
                            result = scraper.extract_text(result.href)
                            if len(result.text_content) > 0:
                                extracted_content = ExtractorAgent(topic, result.text_content)
                                if extracted_content is not None:
                                    print_if_not_quiet(f"  ✅ Extracted {len(extracted_content)} characters of content")
                                    gathered_results.append(extracted_content)

                progress.advance(pipeline_task)
                print_if_not_quiet(f"✅ Gathered {len(gathered_results)} content pieces")

                # Step 3: Generate Script from gathered content
                if gathered_results:
                    progress.update(pipeline_task, description="📝 Generating video script...")
                    try:
                        # Initialize retry mechanism for script generation
                        script_retry = create_llm_retry_mechanism()

                        script_writer = ScriptWriter()

                        # Combine all gathered results into context for script generation
                        combined_content = " ".join(gathered_results)
                        script_prompt = f"Topic: {topic}\n\nResearch Content:\n{combined_content}"

                        # Generate script with retry mechanism
                        def generate_script_with_retry():
                            return script_writer.generate_script(script_prompt, num_scenes=5)

                        script = script_retry.retry_operation(
                            generate_script_with_retry,
                            exception_types=(ScriptParserError, Exception),
                            operation_name="script_generation"
                        )
                        progress.advance(pipeline_task)

                        print_if_not_quiet(f"✅ Generated script with {len(script.scenes)} scenes")

                        # Display script preview (only if not quiet)
                        if not quiet_mode:
                            script_table = Table(title="📋 Script Preview", box=box.ROUNDED, border_style="blue")
                            script_table.add_column("Scene", style="bold cyan", width=8)
                            script_table.add_column("Text", style="white", width=50)
                            script_table.add_column("Image Prompt", style="green", width=40)

                            for i, scene in enumerate(script.scenes, 1):
                                script_table.add_row(
                                    f"Scene {i}",
                                    scene.text[:47] + "..." if len(scene.text) > 50 else scene.text,
                                    scene.image_prompt[:37] + "..." if len(scene.image_prompt) > 40 else scene.image_prompt
                                )

                            console.print(script_table)

                        # Validate the generated script
                        is_valid, issues = script_writer.validate_script(script)
                        if is_valid:
                            print_if_not_quiet("✅ [bold green]Script validation: PASSED")

                            # Initialize variables
                            generated_images = []
                            generated_audio = []
                            final_video_success = False

                            # Step 4: Generate images for each scene
                            progress.update(pipeline_task, description="🎨 Generating scene images...")

                            # Create and clear temp directory
                            temp_dir = Path("temp")

                            # Clear temp directory if it exists
                            if temp_dir.exists():
                                shutil.rmtree(temp_dir)
                                print_if_not_quiet("  🧹 Cleared existing temp directory")

                            # Create fresh temp directory
                            temp_dir.mkdir(parents=True, exist_ok=True)

                            # Initialize image generator
                            image_gen = GeminiImageGen(
                                output_dir=str(temp_dir),
                                config=None  # Uses default config with 9:16 aspect ratio
                            )

                            # Create sub-progress for image generation
                            image_task = progress.add_task("🖼️  Generating images", total=len(script.scenes))

                            # Initialize retry mechanism for image generation
                            image_retry = create_image_retry_mechanism()

                            for i, scene in enumerate(script.scenes, 1):
                                try:
                                    progress.update(image_task, description=f"🖼️  Generating image {i}/{len(script.scenes)}")

                                    # Generate image for this scene using retry mechanism
                                    def generate_scene_image():
                                        return image_gen.generate_image(
                                            prompt=scene.image_prompt,
                                            output_filename=f"SCENE_IMAGE_{i:03d}"
                                        )

                                    image = image_retry.retry_operation(
                                        generate_scene_image,
                                        exception_types=(GeminiImageGenError, Exception),
                                        operation_name=f"image_generation_scene_{i}"
                                    )

                                    generated_images.append(image)
                                    progress.advance(image_task)
                                    print_if_not_quiet(f"  ✅ Scene {i} image generated: [dim]{image.file_path}[/dim]")

                                except RetryError as e:
                                    print_if_not_quiet(f"  ❌ Failed to generate image for scene {i} after {e.attempt_count} attempts: {e.last_exception}")
                                    progress.advance(image_task)
                                    continue
                                except Exception as e:
                                    print_if_not_quiet(f"  ❌ Unexpected error generating image for scene {i}: {e}")
                                    progress.advance(image_task)
                                    continue

                            progress.advance(pipeline_task)
                            print_if_not_quiet(f"🎉 [bold green]Successfully generated {len(generated_images)}/{len(script.scenes)} scene images")

                            # Step 5: Generate audio for each scene
                            progress.update(pipeline_task, description="🎤 Generating scene audio...")

                            # Initialize TTS client
                            tts_client = create_tts_client(
                                language_code=LanguageCode.AMERICAN_ENGLISH,
                                voice="af_heart",
                                speed=1.0,
                                output_dir=str(temp_dir)
                            )

                            # Create sub-progress for audio generation
                            audio_task = progress.add_task("🎵 Generating audio", total=len(script.scenes))

                            # Initialize retry mechanism for audio generation
                            audio_retry = create_llm_retry_mechanism()

                            for i, scene in enumerate(script.scenes, 1):
                                try:
                                    progress.update(audio_task, description=f"🎵 Generating audio {i}/{len(script.scenes)}")

                                    # Generate audio for this scene using retry mechanism
                                    def generate_scene_audio():
                                        return tts_client.generate_audio(
                                            text=scene.text,
                                            output_filename=f"SCENE_AUDIO_{i:03d}"
                                        )

                                    audio = audio_retry.retry_operation(
                                        generate_scene_audio,
                                        exception_types=(KokoroTTSError, Exception),
                                        operation_name=f"audio_generation_scene_{i}"
                                    )

                                    generated_audio.append(audio)
                                    progress.advance(audio_task)
                                    print_if_not_quiet(f"  ✅ Scene {i} audio generated: [dim]{audio.file_path}[/dim]")

                                except RetryError as e:
                                    print_if_not_quiet(f"  ❌ Failed to generate audio for scene {i} after {e.attempt_count} attempts: {e.last_exception}")
                                    progress.advance(audio_task)
                                    continue
                                except Exception as e:
                                    print_if_not_quiet(f"  ❌ Unexpected error generating audio for scene {i}: {e}")
                                    progress.advance(audio_task)
                                    continue

                            progress.advance(pipeline_task)
                            print_if_not_quiet(f"🎉 [bold green]Successfully generated {len(generated_audio)}/{len(script.scenes)} scene audio files")

                            # Step 6: Only proceed to video assembly if both images and audio were generated successfully
                            if generated_images and generated_audio and len(generated_images) == len(generated_audio):
                                progress.update(pipeline_task, description="🎬 Assembling video from scenes...")

                                # Initialize VideoAssembler with YouTube Shorts configuration
                                video_config = VideoConfig(
                                    width=1080,
                                    height=1920,  # 9:16 aspect ratio for YouTube Shorts
                                    fps=30
                                )

                                video_assembler = VideoAssembler(
                                    config=video_config,
                                    output_dir=str(temp_dir)
                                )

                                # Assemble video using directory-based approach
                                video_metadata = video_assembler.assemble_from_directories(
                                    images_dir=str(temp_dir),
                                    audio_dir=str(temp_dir),
                                    output_filename="assembled_video",
                                    image_pattern="SCENE_IMAGE_*.png",
                                    audio_pattern="SCENE_AUDIO_*.wav"
                                )

                                progress.advance(pipeline_task)

                                # Display video info (only if not quiet)
                                if not quiet_mode:
                                    video_info = Table(title="🎬 Video Assembly Complete", box=box.ROUNDED, border_style="green")
                                    video_info.add_column("Property", style="bold white")
                                    video_info.add_column("Value", style="bold green")

                                    video_info.add_row("File Path", video_metadata.file_path)
                                    video_info.add_row("Duration", f"{video_metadata.duration_seconds:.2f}s")
                                    video_info.add_row("File Size", f"{video_metadata.file_size / (1024*1024):.1f} MB")
                                    video_info.add_row("Resolution", f"{video_metadata.width}x{video_metadata.height}")
                                    video_info.add_row("FPS", str(video_metadata.fps))

                                    console.print(video_info)

                                # Step 7: Generate YouTube metadata
                                progress.update(pipeline_task, description="📋 Generating YouTube metadata...")
                                try:
                                    # Initialize retry mechanism for metadata generation
                                    metadata_retry = create_llm_retry_mechanism()

                                    metadata_agent = YouTubeMetadataAgent()

                                    # Generate metadata with retry mechanism
                                    def generate_metadata_with_retry():
                                        return metadata_agent.generate_metadata(
                                            script=script,
                                            topic=topic,
                                            content_style="educational"
                                        )

                                    youtube_metadata = metadata_retry.retry_operation(
                                        generate_metadata_with_retry,
                                        exception_types=(YouTubeMetadataError, Exception),
                                        operation_name="youtube_metadata_generation"
                                    )

                                    # Save metadata to file
                                    metadata_file_path = temp_dir / "youtube_metadata.json"
                                    with open(metadata_file_path, 'w', encoding='utf-8') as f:
                                        f.write(youtube_metadata.to_json())

                                    progress.advance(pipeline_task)
                                    print_if_not_quiet(f"✅ [bold green]YouTube metadata generated and saved to: {metadata_file_path}")

                                    # Display metadata info (only if not quiet)
                                    if not quiet_mode:
                                        metadata_info = Table(title="📋 YouTube Metadata Generated", box=box.ROUNDED, border_style="blue")
                                        metadata_info.add_column("Property", style="bold white")
                                        metadata_info.add_column("Value", style="bold cyan")

                                        metadata_info.add_row("Title", youtube_metadata.title)
                                        metadata_info.add_row("Tags", ", ".join(youtube_metadata.tags[:5]) + ("..." if len(youtube_metadata.tags) > 5 else ""))
                                        metadata_info.add_row("Description Length", f"{len(youtube_metadata.description)} characters")
                                        metadata_info.add_row("File Path", str(metadata_file_path))

                                        console.print(metadata_info)

                                except RetryError as e:
                                    print_if_not_quiet(f"⚠️  [bold yellow]YouTube metadata generation failed after {e.attempt_count} attempts: {e.last_exception}")
                                except YouTubeMetadataError as e:
                                    print_if_not_quiet(f"⚠️  [bold yellow]YouTube metadata generation failed: {e}")
                                except Exception as e:
                                    print_if_not_quiet(f"⚠️  [bold yellow]Unexpected error generating YouTube metadata: {e}")

                                # Step 8: Upload to YouTube (if enabled)
                                if enable_upload:
                                    progress.update(pipeline_task, description="📤 Uploading to YouTube...")
                                    try:
                                        # Initialize YouTube uploader with retry mechanism
                                        upload_retry = create_llm_retry_mechanism()

                                        def upload_to_youtube():
                                            uploader = YouTubeUploader(
                                                profile_name="ytfinance_bot",
                                                headless=False,  # Keep visible for first-time login
                                                timeout=60
                                            )

                                            try:
                                                # Ensure logged in
                                                if not uploader.ensure_logged_in():
                                                    raise YouTubeUploaderError("Failed to log in to YouTube")

                                                # Prepare upload configuration
                                                upload_config = UploadConfig(
                                                    privacy="unlisted",  # Start with unlisted for safety
                                                    made_for_kids=False,
                                                    category="Education",
                                                    enable_comments=True,
                                                    enable_ratings=True
                                                )

                                                # Upload the video (ensure absolute path for Selenium)
                                                video_path = Path(video_metadata.file_path).resolve()
                                                result = uploader.upload_video(
                                                    video_path=str(video_path),
                                                    title=youtube_metadata.title,
                                                    description=youtube_metadata.description,
                                                    tags=youtube_metadata.tags,
                                                    config=upload_config
                                                )

                                                return result

                                            finally:
                                                uploader.close()

                                        upload_result = upload_retry.retry_operation(
                                            upload_to_youtube,
                                            exception_types=(YouTubeUploaderError, Exception),
                                            operation_name="youtube_upload"
                                        )

                                        if upload_result.success:
                                            progress.advance(pipeline_task)
                                            print_if_not_quiet(f"✅ [bold green]Video uploaded to YouTube: {upload_result.video_url}")

                                            # Save upload info to file
                                            upload_info_file = temp_dir / "youtube_upload_info.json"
                                            upload_info = {
                                                "video_url": upload_result.video_url,
                                                "video_id": upload_result.video_id,
                                                "upload_time": upload_result.upload_time.isoformat() if upload_result.upload_time else None,
                                                "title": youtube_metadata.title,
                                                "privacy": "unlisted"
                                            }

                                            with open(upload_info_file, 'w', encoding='utf-8') as f:
                                                json.dump(upload_info, f, indent=2)

                                            # Display upload info (only if not quiet)
                                            if not quiet_mode:
                                                upload_info_table = Table(title="📤 YouTube Upload Complete", box=box.ROUNDED, border_style="cyan")
                                                upload_info_table.add_column("Property", style="bold white")
                                                upload_info_table.add_column("Value", style="bold cyan")

                                                upload_info_table.add_row("Video URL", upload_result.video_url or "N/A")
                                                upload_info_table.add_row("Video ID", upload_result.video_id or "N/A")
                                                upload_info_table.add_row("Privacy", "Unlisted")
                                                upload_info_table.add_row("Upload Info File", str(upload_info_file))

                                                console.print(upload_info_table)

                                            session_stats["Videos Uploaded"] = session_stats.get("Videos Uploaded", 0) + 1
                                        else:
                                            print_if_not_quiet(f"⚠️  [bold yellow]YouTube upload failed: {upload_result.error_message}")

                                    except RetryError as e:
                                        print_if_not_quiet(f"⚠️  [bold yellow]YouTube upload failed after {e.attempt_count} attempts: {e.last_exception}")
                                    except Exception as e:
                                        print_if_not_quiet(f"⚠️  [bold yellow]Unexpected error during YouTube upload: {e}")
                                else:
                                    print_if_not_quiet("⚠️  [bold yellow]YouTube upload disabled (--no-upload flag)")

                                # Track the video in our history
                                video_record = video_tracker.add_video(
                                    title=youtube_metadata.title if 'youtube_metadata' in locals() else f"Video: {topic}",
                                    topic=topic,
                                    video_id=upload_result.video_id if 'upload_result' in locals() and upload_result.success else None,
                                    video_url=upload_result.video_url if 'upload_result' in locals() and upload_result.success else None,
                                    upload_status="uploaded" if 'upload_result' in locals() and upload_result.success else "pending",
                                    privacy="unlisted",
                                    tags=youtube_metadata.tags if 'youtube_metadata' in locals() else [],
                                    description_length=len(youtube_metadata.description) if 'youtube_metadata' in locals() else 0,
                                    video_duration=video_metadata.duration_seconds if 'video_metadata' in locals() else None,
                                    file_path=str(video_metadata.file_path) if 'video_metadata' in locals() else None
                                )

                                print_if_not_quiet(f"📊 [bold blue]Video tracked in history: {video_record.title}")

                                final_video_success = True
                                session_stats["Videos Created"] += 1
                            else:
                                print_if_not_quiet(f"⚠️  [bold yellow]Skipping video assembly - missing assets (images: {len(generated_images)}, audio: {len(generated_audio)})")

                        else:
                            print_if_not_quiet("❌ [bold red]Script validation: FAILED")
                            if not quiet_mode:
                                for issue in issues:
                                    console.print(f"  - {issue}")

                    except RetryError as e:
                        print_if_not_quiet(f"❌ [bold red]Script generation failed after {e.attempt_count} attempts: {e.last_exception}")
                    except ScriptParserError as e:
                        print_if_not_quiet(f"❌ [bold red]Script generation failed: {e}")
                    except Exception as e:
                        print_if_not_quiet(f"❌ [bold red]Unexpected error during script generation: {e}")
                else:
                    print_if_not_quiet("❌ [bold red]No content gathered, skipping script generation")

                # Display completion status (only if not quiet)
                if not quiet_mode:
                    if 'final_video_success' in locals() and final_video_success:

                        # Success panel
                        success_panel = Panel(
                            Align.center(
                                Text.assemble(
                                    "🎉 ", ("TOPIC COMPLETED SUCCESSFULLY!", "bold green"), "\n\n",
                                    f"Topic: {topic}\n",
                                    f"✅ Generated {len(generated_images)} scene images\n",
                                    f"✅ Generated {len(generated_audio)} scene audio files\n",
                                    f"✅ Assembled video\n",
                                    f"✅ Generated YouTube metadata\n",
                                    f"✅ {'Uploaded to YouTube' if enable_upload else 'Video ready (upload disabled)'}\n",
                                    f"📁 Files saved in: {temp_dir if 'temp_dir' in locals() else 'temp'}"
                                )
                            ),
                            box=box.DOUBLE,
                            border_style="bright_green",
                            padding=(1, 2)
                        )
                        console.print(success_panel)
                    elif 'generated_images' in locals() and 'generated_audio' in locals():
                        if generated_images and generated_audio:
                            console.print(f"⚠️  [bold yellow]Topic '{topic}' partially completed - assets generated but video assembly failed")
                        elif generated_images:
                            console.print(f"⚠️  [bold yellow]Topic '{topic}' partially completed - images generated but audio failed")
                        else:
                            console.print(f"❌ [bold red]Topic '{topic}' failed - no assets generated")
                    else:
                        console.print(f"❌ [bold red]Topic '{topic}' failed - pipeline error")

                    # Display session statistics
                    console.print(create_stats_table(session_stats))
                    console.print()

                    # Single video creation complete - exit the workflow
                    console.print("🎬 [bold green]Single video creation workflow completed!")

                break

        except KeyboardInterrupt:
            if not quiet_mode:
                console.print("\n🛑 [bold red]Process interrupted by user")
                console.print(create_stats_table(session_stats))
            break
        except Exception as e:
            if not quiet_mode:
                console.print(f"❌ [bold red]Unexpected error in main loop: {e}")
                console.print(create_stats_table(session_stats))
            time.sleep(60)  # Wait before retrying


def show_video_statistics(video_tracker: VideoTracker):
    """Display video creation statistics"""
    stats = video_tracker.get_statistics()

    console.print("\n📊 [bold blue]Video Creation Statistics")
    console.print("=" * 50)

    stats_table = Table(title="📈 Video Statistics", box=box.ROUNDED, border_style="blue")
    stats_table.add_column("Metric", style="bold white")
    stats_table.add_column("Value", style="bold cyan")

    stats_table.add_row("Total Videos", str(stats["total_videos"]))
    stats_table.add_row("Uploaded Videos", str(stats["uploaded_videos"]))
    stats_table.add_row("Pending Videos", str(stats["pending_videos"]))
    stats_table.add_row("Failed Videos", str(stats["failed_videos"]))
    stats_table.add_row("Today's Videos", str(stats["today_videos"]))
    stats_table.add_row("This Week", str(stats["week_videos"]))
    stats_table.add_row("This Month", str(stats["month_videos"]))
    stats_table.add_row("Upload Success Rate", f"{stats['upload_success_rate']}%")

    console.print(stats_table)


def list_recent_videos(video_tracker: VideoTracker, days: int = 7):
    """List recent videos"""
    recent_videos = video_tracker.get_recent_videos(days)

    console.print(f"\n📋 [bold blue]Recent Videos (Last {days} days)")
    console.print("=" * 50)

    if not recent_videos:
        console.print("No videos found in the specified time period.")
        return

    videos_table = Table(title=f"📹 Recent Videos ({len(recent_videos)} found)", box=box.ROUNDED, border_style="green")
    videos_table.add_column("Date", style="dim")
    videos_table.add_column("Title", style="bold white")
    videos_table.add_column("Topic", style="cyan")
    videos_table.add_column("Status", style="bold")
    videos_table.add_column("URL", style="blue")

    for video in recent_videos:
        created_date = datetime.fromisoformat(video.created_at).strftime("%m/%d %H:%M")

        # Color code status
        status_color = {
            "uploaded": "green",
            "pending": "yellow",
            "failed": "red"
        }.get(video.upload_status, "white")

        status_text = f"[{status_color}]{video.upload_status.upper()}[/{status_color}]"

        url_text = video.video_url[:50] + "..." if video.video_url and len(video.video_url) > 50 else (video.video_url or "N/A")

        videos_table.add_row(
            created_date,
            video.title[:40] + "..." if len(video.title) > 40 else video.title,
            video.topic[:30] + "..." if len(video.topic) > 30 else video.topic,
            status_text,
            url_text
        )

    console.print(videos_table)


def reset_video_history(video_tracker: VideoTracker):
    """Reset video history"""
    console.print("\n🗑️  [bold red]Resetting Video History")
    console.print("=" * 50)

    # Get current stats before reset
    stats = video_tracker.get_statistics()

    if stats["total_videos"] == 0:
        console.print("No video history to reset.")
        return

    # Export current data before reset
    export_file = f"video_history_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    video_tracker.export_data(export_file)
    console.print(f"📁 Backup created: {export_file}")

    # Reset the data
    video_tracker.reset_data()

    console.print(f"✅ Reset complete! Cleared {stats['total_videos']} video records.")
    console.print("🔄 Ready for new video creation cycle.")


def upload_current_video(video_tracker: VideoTracker, quiet_mode: bool = False):
    """Upload the current video in temp directory"""

    # Helper function for conditional console output
    def print_if_not_quiet(*args, **kwargs):
        if not quiet_mode:
            console.print(*args, **kwargs)

    print_if_not_quiet("\n📤 [bold blue]Uploading Current Video")
    print_if_not_quiet("=" * 50)

    # Check for video file in temp directory
    temp_dir = Path("temp")
    if not temp_dir.exists():
        print_if_not_quiet("❌ [bold red]Temp directory not found")
        print_if_not_quiet("💡 Run 'python workflow.py --run --no-upload' first to create a video")
        return

    # Look for video file
    video_patterns = ["assembled_video.mp4", "*.mp4", "*.avi", "*.mov"]
    video_file = None

    for pattern in video_patterns:
        video_files = list(temp_dir.glob(pattern))
        if video_files:
            video_file = video_files[0]  # Take the first match
            break

    if not video_file or not video_file.exists():
        print_if_not_quiet("❌ [bold red]No video file found in temp directory")
        print_if_not_quiet("💡 Run 'python workflow.py --run --no-upload' first to create a video")
        return

    print_if_not_quiet(f"📹 Found video: [dim]{video_file}[/dim]")

    # Look for metadata file
    metadata_file = temp_dir / "youtube_metadata.json"
    if not metadata_file.exists():
        print_if_not_quiet("⚠️  [bold yellow]No metadata file found, using default metadata")
        # Create default metadata
        youtube_metadata = type('YouTubeMetadata', (), {
            'title': f"Finance Video - {datetime.now().strftime('%Y-%m-%d')}",
            'description': "Automated finance video created with YTFinance",
            'tags': ["finance", "investing", "youtubeshorts", "shorts"]
        })()
    else:
        # Load metadata from file
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata_dict = json.load(f)

            youtube_metadata = type('YouTubeMetadata', (), metadata_dict)()
            print_if_not_quiet(f"📋 Loaded metadata: [dim]{youtube_metadata.title[:50]}...[/dim]")
        except Exception as e:
            print_if_not_quiet(f"⚠️  [bold yellow]Error loading metadata: {e}")
            print_if_not_quiet("Using default metadata instead")
            youtube_metadata = type('YouTubeMetadata', (), {
                'title': f"Finance Video - {datetime.now().strftime('%Y-%m-%d')}",
                'description': "Automated finance video created with YTFinance",
                'tags': ["finance", "investing", "youtubeshorts", "shorts"]
            })()

    # Initialize retry mechanism for upload
    upload_retry = create_llm_retry_mechanism()

    try:
        print_if_not_quiet("🚀 [bold green]Starting upload to YouTube...")

        def upload_to_youtube():
            uploader = YouTubeUploader(
                profile_name="ytfinance_bot",
                headless=False,  # Keep visible for login verification
                timeout=60
            )

            try:
                # Ensure logged in
                if not uploader.ensure_logged_in():
                    raise YouTubeUploaderError("Failed to log in to YouTube")

                # Prepare upload configuration
                upload_config = UploadConfig(
                    privacy="unlisted",  # Start with unlisted for safety
                    made_for_kids=False,
                    category="Education",
                    enable_comments=True,
                    enable_ratings=True
                )

                # Upload the video (ensure absolute path)
                video_path = video_file.resolve()
                result = uploader.upload_video(
                    video_path=str(video_path),
                    title=youtube_metadata.title,
                    description=youtube_metadata.description,
                    tags=youtube_metadata.tags,
                    config=upload_config
                )

                return result

            finally:
                uploader.close()

        upload_result = upload_retry.retry_operation(
            upload_to_youtube,
            exception_types=(YouTubeUploaderError, Exception),
            operation_name="youtube_upload"
        )

        if upload_result.success:
            print_if_not_quiet(f"✅ [bold green]Video uploaded successfully!")
            print_if_not_quiet(f"🔗 Video URL: {upload_result.video_url}")

            # Save upload info to file
            upload_info_file = temp_dir / "youtube_upload_info.json"
            upload_info = {
                "video_url": upload_result.video_url,
                "video_id": upload_result.video_id,
                "upload_time": upload_result.upload_time.isoformat() if upload_result.upload_time else None,
                "title": youtube_metadata.title,
                "privacy": "unlisted"
            }

            with open(upload_info_file, 'w', encoding='utf-8') as f:
                json.dump(upload_info, f, indent=2)

            print_if_not_quiet(f"📁 Upload info saved: [dim]{upload_info_file}[/dim]")

            # Update video tracker if we can find the video record
            try:
                # Try to find and update the video record
                for video in video_tracker.videos:
                    if video.upload_status == "pending" and video.file_path and Path(video.file_path).name == video_file.name:
                        video_tracker.update_video_upload(
                            title=video.title,
                            video_id=upload_result.video_id,
                            video_url=upload_result.video_url,
                            upload_status="uploaded"
                        )
                        print_if_not_quiet(f"📊 [bold blue]Updated video record in tracker")
                        break
            except Exception as e:
                print_if_not_quiet(f"⚠️  [bold yellow]Could not update video tracker: {e}")

            # Display success panel
            if not quiet_mode:
                success_panel = Panel(
                    Align.center(
                        Text.assemble(
                            "🎉 ", ("VIDEO UPLOADED SUCCESSFULLY!", "bold green"), "\n\n",
                            f"Title: {youtube_metadata.title}\n",
                            f"Video ID: {upload_result.video_id or 'N/A'}\n",
                            f"URL: {upload_result.video_url or 'N/A'}\n",
                            f"Privacy: Unlisted\n",
                            f"Upload Time: {upload_result.upload_time.strftime('%Y-%m-%d %H:%M:%S') if upload_result.upload_time else 'N/A'}"
                        )
                    ),
                    box=box.DOUBLE,
                    border_style="bright_green",
                    padding=(1, 2)
                )
                console.print(success_panel)
        else:
            print_if_not_quiet(f"❌ [bold red]Upload failed: {upload_result.error_message}")

    except RetryError as e:
        print_if_not_quiet(f"❌ [bold red]Upload failed after {e.attempt_count} attempts: {e.last_exception}")
    except Exception as e:
        print_if_not_quiet(f"❌ [bold red]Unexpected error during upload: {e}")


def main():
    """Main entry point with CLI argument handling"""
    args = parse_arguments()

    # Set up logging level - clean CLI output by default
    if args.verbose:
        # Verbose mode: show all debug information
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    elif args.quiet:
        # Quiet mode: suppress everything except critical errors
        logging.basicConfig(level=logging.CRITICAL)
        logging.getLogger().setLevel(logging.CRITICAL)
        for logger_name in logging.Logger.manager.loggerDict:
            logging.getLogger(logger_name).setLevel(logging.CRITICAL)
    else:
        # Default mode: suppress all logging output for clean Rich console experience
        logging.basicConfig(level=logging.CRITICAL)
        logging.getLogger().setLevel(logging.CRITICAL)
        for logger_name in logging.Logger.manager.loggerDict:
            logging.getLogger(logger_name).setLevel(logging.CRITICAL)

    # Initialize video tracker
    video_tracker = VideoTracker(args.storage_file)

    # Handle different commands
    if args.reset:
        reset_video_history(video_tracker)

    elif args.stats:
        show_video_statistics(video_tracker)

    elif args.list:
        list_recent_videos(video_tracker, args.days)

    elif args.upload:
        upload_current_video(video_tracker, quiet_mode=args.quiet)

    elif args.run:
        if not args.quiet:
            console.print("🚀 [bold green]Starting YTFinance Video Creation Workflow")
            console.print("=" * 60)

            if args.no_upload:
                console.print("⚠️  [bold yellow]Upload disabled (--no-upload flag)")

        run_video_creation_workflow(video_tracker, enable_upload=not args.no_upload, quiet_mode=args.quiet)

    else:
        # No command specified, show help
        console.print("🎬 [bold blue]YTFinance - Automated YouTube Finance Video Creator")
        console.print("=" * 60)
        console.print()
        console.print("Available commands:")
        console.print("  --run              Create and upload a new video")
        console.print("  --reset            Reset video history (for scheduled cleanup)")
        console.print("  --stats            Show video creation statistics")
        console.print("  --list             List recent videos")
        console.print("  --upload           Upload current video in temp directory")
        console.print()
        console.print("Options:")
        console.print("  --no-upload        Create video without uploading")
        console.print("  --days N           Number of days for --list (default: 7)")
        console.print("  --verbose, -v      Enable verbose logging (debug mode)")
        console.print("  --quiet, -q        Suppress all output except errors")
        console.print()
        console.print("Examples:")
        console.print("  python workflow.py --run")
        console.print("  python workflow.py --stats")
        console.print("  python workflow.py --list --days 30")
        console.print("  python workflow.py --reset")
        console.print("  python workflow.py --upload")


if __name__ == "__main__":
    main()

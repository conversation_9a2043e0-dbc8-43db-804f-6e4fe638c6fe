#!/usr/bin/env python3
"""
Test script that directly uses the YouTubeUploader class
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the path
sys.path.append(os.getcwd())

try:
    from Helpers.YouTubeUploader import YouTubeUploader, UploadConfig
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def setup_logging():
    """Setup detailed logging"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_uploader.log')
        ]
    )

def test_direct_uploader():
    """Test using the YouTubeUploader class directly"""
    print("🧪 Testing YouTubeUploader class directly...")
    
    # Setup logging
    setup_logging()
    
    # Create uploader instance
    uploader = YouTubeUploader(
        profile_name="ytfinance_bot",
        headless=False,
        timeout=30
    )
    
    try:
        # Check if logged in
        print("🔍 Checking login status...")
        if not uploader.ensure_logged_in():
            print("❌ Not logged in!")
            return False
        
        print("✅ Logged in successfully!")
        
        # Test video path
        video_path = os.path.join("temp", "assembled_video.mp4")
        if not os.path.exists(video_path):
            print(f"❌ Video file not found: {video_path}")
            return False
        
        print(f"📹 Found video: {video_path}")
        
        # Test metadata
        title = "Test Video from Direct YouTubeUploader"
        description = "This is a test video uploaded using the YouTubeUploader class directly."
        tags = ["test", "youtube", "automation"]
        
        print(f"📋 Title: {title}")
        print(f"📋 Description: {description[:50]}...")
        print(f"📋 Tags: {tags}")
        
        # Create upload config
        config = UploadConfig(
            privacy="unlisted",  # Use unlisted for testing
            made_for_kids=False
        )
        
        print("🚀 Starting upload...")
        
        # Perform upload
        result = uploader.upload_video(
            video_path=video_path,
            title=title,
            description=description,
            tags=tags,
            config=config
        )
        
        print(f"📊 Upload result: {result}")
        
        if result.success:
            print("✅ Upload completed successfully!")
            print(f"🔗 Video URL: {result.video_url}")
            print(f"🆔 Video ID: {result.video_id}")
            return True
        else:
            print(f"❌ Upload failed: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    finally:
        print("🔄 Closing uploader...")
        uploader.close()

def main():
    """Main test function"""
    success = test_direct_uploader()
    
    if success:
        print("✅ Direct YouTubeUploader test completed successfully!")
    else:
        print("❌ Direct YouTubeUploader test failed!")
        print("📄 Check test_uploader.log for detailed logs")

if __name__ == "__main__":
    main()

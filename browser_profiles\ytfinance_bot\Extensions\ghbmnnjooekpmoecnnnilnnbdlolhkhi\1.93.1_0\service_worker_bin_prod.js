'use strict';function aa(){return function(a){return a}}function k(){return function(){}}function p(a){return function(){return this[a]}}function ba(a){return function(){return a}}var r;function ca(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var da=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ea(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var fa=ea(this);function t(a,b){if(b)a:{var c=fa;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&da(c,a,{configurable:!0,writable:!0,value:b})}}
t("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.g=f;da(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=p("g");var d="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",e=0;return b});
t("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=fa[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&da(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ha(ca(this))}})}return a});function ha(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
var ia=typeof Object.create=="function"?Object.create:function(a){function b(){}b.prototype=a;return new b},ja;if(typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ka;a:{var ma={a:!0},na={};try{na.__proto__=ma;ka=na.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var oa=ja;
function u(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(oa)oa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Z=b.prototype}function v(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ca(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}
function pa(a){if(!(a instanceof Array)){a=v(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}function qa(a){return ra(a,a)}function ra(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a}function sa(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b}t("globalThis",function(a){return a||fa});
t("Promise",function(a){function b(g){this.g=0;this.l=void 0;this.j=[];this.A=!1;var h=this.o();try{g(h.resolve,h.reject)}catch(l){h.reject(l)}}function c(){this.g=null}function d(g){return g instanceof b?g:new b(function(h){h(g)})}if(a)return a;c.prototype.j=function(g){if(this.g==null){this.g=[];var h=this;this.l(function(){h.v()})}this.g.push(g)};var e=fa.setTimeout;c.prototype.l=function(g){e(g,0)};c.prototype.v=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var l=
g[h];g[h]=null;try{l()}catch(m){this.o(m)}}}this.g=null};c.prototype.o=function(g){this.l(function(){throw g;})};b.prototype.o=function(){function g(m){return function(n){l||(l=!0,m.call(h,n))}}var h=this,l=!1;return{resolve:g(this.M),reject:g(this.v)}};b.prototype.M=function(g){if(g===this)this.v(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof b)this.O(g);else{a:switch(typeof g){case "object":var h=g!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.K(g):this.C(g)}};
b.prototype.K=function(g){var h=void 0;try{h=g.then}catch(l){this.v(l);return}typeof h=="function"?this.S(h,g):this.C(g)};b.prototype.v=function(g){this.B(2,g)};b.prototype.C=function(g){this.B(1,g)};b.prototype.B=function(g,h){if(this.g!=0)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.g);this.g=g;this.l=h;this.g===2&&this.U();this.G()};b.prototype.U=function(){var g=this;e(function(){if(g.I()){var h=fa.console;typeof h!=="undefined"&&h.error(g.l)}},1)};b.prototype.I=
function(){if(this.A)return!1;var g=fa.CustomEvent,h=fa.Event,l=fa.dispatchEvent;if(typeof l==="undefined")return!0;typeof g==="function"?g=new g("unhandledrejection",{cancelable:!0}):typeof h==="function"?g=new h("unhandledrejection",{cancelable:!0}):(g=fa.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.l;return l(g)};b.prototype.G=function(){if(this.j!=null){for(var g=0;g<this.j.length;++g)f.j(this.j[g]);this.j=null}};var f=new c;
b.prototype.O=function(g){var h=this.o();g.qa(h.resolve,h.reject)};b.prototype.S=function(g,h){var l=this.o();try{g.call(h,l.resolve,l.reject)}catch(m){l.reject(m)}};b.prototype.then=function(g,h){function l(x,q){return typeof x=="function"?function(H){try{m(x(H))}catch(Y){n(Y)}}:q}var m,n,w=new b(function(x,q){m=x;n=q});this.qa(l(g,m),l(h,n));return w};b.prototype.catch=function(g){return this.then(void 0,g)};b.prototype.qa=function(g,h){function l(){switch(m.g){case 1:g(m.l);break;case 2:h(m.l);
break;default:throw Error("Unexpected state: "+m.g);}}var m=this;this.j==null?f.j(l):this.j.push(l);this.A=!0};b.resolve=d;b.reject=function(g){return new b(function(h,l){l(g)})};b.race=function(g){return new b(function(h,l){for(var m=v(g),n=m.next();!n.done;n=m.next())d(n.value).qa(h,l)})};b.all=function(g){var h=v(g),l=h.next();return l.done?d([]):new b(function(m,n){function w(H){return function(Y){x[H]=Y;q--;q==0&&m(x)}}var x=[],q=0;do x.push(void 0),q++,d(l.value).qa(w(x.length-1),n),l=h.next();
while(!l.done)})};return b});function ta(a,b){return Object.prototype.hasOwnProperty.call(a,b)}t("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});t("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});
t("WeakMap",function(a){function b(l){this.g=(h+=Math.random()+1).toString();if(l){l=v(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}}function c(){}function d(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function e(l){if(!ta(l,g)){var m=new c;da(l,g,{value:m})}}function f(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof c)return n;Object.isExtensible(n)&&e(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),
n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(w){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(l,m){if(!d(l))throw Error("Invalid WeakMap key");e(l);if(!ta(l,g))throw Error("WeakMap key fail: "+l);l[g][this.g]=m;return this};b.prototype.get=function(l){return d(l)&&ta(l,g)?l[g][this.g]:void 0};b.prototype.has=function(l){return d(l)&&ta(l,
g)&&ta(l[g],this.g)};b.prototype.delete=function(l){return d(l)&&ta(l,g)&&ta(l[g],this.g)?delete l[g][this.g]:!1};return b});
t("Map",function(a){function b(){var h={};return h.previous=h.next=h.head=h}function c(h,l){var m=h[1];return ha(function(){if(m){for(;m.head!=h[1];)m=m.previous;for(;m.next!=m.head;)return m=m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})}function d(h,l){var m=l&&typeof l;m=="object"||m=="function"?f.has(l)?m=f.get(l):(m=""+ ++g,f.set(l,m)):m="p_"+l;var n=h[0][m];if(n&&ta(h[0],m))for(h=0;h<n.length;h++){var w=n[h];if(l!==l&&w.key!==w.key||l===w.key)return{id:m,list:n,index:h,entry:w}}return{id:m,
list:n,index:-1,entry:void 0}}function e(h){this[0]={};this[1]=b();this.size=0;if(h){h=v(h);for(var l;!(l=h.next()).done;)l=l.value,this.set(l[0],l[1])}}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),l=new a(v([[h,"s"]]));if(l.get(h)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=
4||n.value[1]!="t"||!m.next().done?!1:!0}catch(w){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,l){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.entry?m.entry.value=l:(m.entry={next:this[1],previous:this[1].previous,head:this[1],key:h,value:l},m.list.push(m.entry),this[1].previous.next=m.entry,this[1].previous=m.entry,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],
h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};e.prototype.clear=function(){this[0]={};this[1]=this[1].previous=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).entry};e.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,
function(h){return h.value})};e.prototype.forEach=function(h,l){for(var m=this.entries(),n;!(n=m.next()).done;)n=n.value,h.call(l,n[1],n[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});
t("Set",function(a){function b(c){this.g=new Map;if(c){c=v(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size}if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(v([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||
f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;b.prototype.add=function(c){c=c===0?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;b.prototype[Symbol.iterator]=
b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});t("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)ta(b,d)&&c.push(b[d]);return c}});t("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
t("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
t("String.prototype.includes",function(a){return a?a:function(b,c){if(this==null)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return(this+"").indexOf(b,c||0)!==-1}});
t("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:aa();var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});t("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)ta(b,d)&&c.push([d,b[d]]);return c}});
t("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});t("Number.MAX_SAFE_INTEGER",ba(9007199254740991));t("Number.MIN_SAFE_INTEGER",ba(-9007199254740991));t("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});t("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});
function ua(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}t("Array.prototype.entries",function(a){return a?a:function(){return ua(this,function(b,c){return[b,c]})}});t("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});
t("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});t("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});t("Array.prototype.keys",function(a){return a?a:function(){return ua(this,aa())}});t("Array.prototype.values",function(a){return a?a:function(){return ua(this,function(b,c){return c})}});
t("String.prototype.matchAll",function(a){return a?a:function(b){if(b instanceof RegExp&&!b.global)throw new TypeError("RegExp passed into String.prototype.matchAll() must have global tag.");var c=new RegExp(b,b instanceof RegExp?void 0:"g"),d=this,e=!1,f={next:function(){if(e)return{value:void 0,done:!0};var g=c.exec(d);if(!g)return e=!0,{value:void 0,done:!0};g[0]===""&&(c.lastIndex+=1);return{value:g,done:!1}}};f[Symbol.iterator]=function(){return f};return f}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var va=va||{},y=this||self;function wa(a){a=a.split(".");for(var b=y,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b}function xa(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"}function ya(a){var b=xa(a);return b=="array"||b=="object"&&typeof a.length=="number"}function za(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}var Aa="closure_uid_"+(Math.random()*1E9>>>0),Ba=0;function Ca(a,b,c){return a.call.apply(a.bind,arguments)}
function Da(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function z(a,b,c){z=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?Ca:Da;return z.apply(null,arguments)}
function Ea(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function Fa(a){(0,eval)(a)}function Ga(a){return a}function A(a,b){function c(){}c.prototype=b.prototype;a.Z=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.rc=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};function Ha(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,Ha);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.g=!0}A(Ha,Error);Ha.prototype.name="CustomError";function Ia(a){y.setTimeout(function(){throw a;},0)};var Ja=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};var Ka,La=wa("CLOSURE_FLAGS"),Ma=La&&La[610401301];Ka=Ma!=null?Ma:!1;function Na(){var a=y.navigator;return a&&(a=a.userAgent)?a:""}var Oa,Pa=y.navigator;Oa=Pa?Pa.userAgentData||null:null;function Qa(a){if(!Ka||!Oa)return!1;for(var b=0;b<Oa.brands.length;b++){var c=Oa.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function B(a){return Na().indexOf(a)!=-1};function Ra(){return Ka?!!Oa&&Oa.brands.length>0:!1}function Sa(){return B("Firefox")||B("FxiOS")}function Ta(){return Ra()?Qa("Chromium"):(B("Chrome")||B("CriOS"))&&!(Ra()?0:B("Edge"))||B("Silk")};function Ua(){return Ka?!!Oa&&!!Oa.platform:!1}function Va(){return B("iPhone")&&!B("iPod")&&!B("iPad")}function Wa(){Va()||B("iPad")||B("iPod")};function Xa(a,b){return Array.prototype.indexOf.call(a,b,void 0)}function Ya(a,b){return Array.prototype.some.call(a,b,void 0)}function Za(a,b){b=Xa(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c}function $a(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(ya(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}};B("Mobile");Ua()||B("Macintosh");Ua()||B("Windows");(Ua()?Oa.platform==="Linux":B("Linux"))||Ua()||B("CrOS");Ua()||B("Android");Va();B("iPad");B("iPod");Wa();Na().toLowerCase().indexOf("kaios");Sa();Va()||B("iPod");B("iPad");!B("Android")||Ta()||Sa()||(Ra()?0:B("Opera"))||B("Silk");Ta();!B("Safari")||Ta()||(Ra()?0:B("Coast"))||(Ra()?0:B("Opera"))||(Ra()?0:B("Edge"))||(Ra()?Qa("Microsoft Edge"):B("Edg/"))||(Ra()?Qa("Opera"):B("OPR"))||Sa()||B("Silk")||B("Android")||Wa();var ab={},bb=null;var cb=typeof Uint8Array!=="undefined",db=typeof btoa==="function",eb={},fb=typeof structuredClone!="undefined";function gb(a,b){if(b!==eb)throw Error("illegal external caller");this.g=a;if(a!=null&&a.length===0)throw Error("ByteString should be constructed with non-empty values");}function hb(){return ib||(ib=new gb(null,eb))}var ib;function jb(a,b,c){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382[b]=c}function kb(a){return a.__closure__error__context__984382||{}};var lb=void 0;function mb(a,b){if(a!=null){var c;var d=(c=lb)!=null?c:lb={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),jb(a,"severity","incident"),Ia(a))}};var nb=typeof Symbol==="function"&&typeof Symbol()==="symbol";function ob(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b}var qb=ob("jas",void 0,!0),rb=ob(void 0,"0di"),sb=ob(void 0,"1oa"),tb=ob(void 0,Symbol()),ub=ob(void 0,"0ubs"),vb=ob(void 0,"0actk"),wb=ob("m_m","uc",!0);Math.max.apply(Math,pa(Object.values({Vb:1,Tb:2,Qb:4,dc:8,nc:16,Zb:32,Fb:64,Ob:128,Mb:256,kc:512,Nb:1024,Pb:2048,ac:4096})));var xb={rb:{value:0,configurable:!0,writable:!0,enumerable:!1}},yb=Object.defineProperties,C=nb?qb:"rb",zb,Ab=[];Bb(Ab,7);zb=Object.freeze(Ab);function Cb(a,b){nb||C in a||yb(a,xb);a[C]|=b}function Bb(a,b){nb||C in a||yb(a,xb);a[C]=b}function Db(a){Cb(a,34);return a};function Eb(){return typeof BigInt==="function"};var Fb={};function Gb(a,b){return b===void 0?a.g!==Hb&&!!(2&(a.D[C]|0)):!!(2&b)&&a.g!==Hb}var Hb={},Ib=Object.freeze({});function Jb(a){a.tc=!0;return a};var Kb=Jb(function(a){return typeof a==="number"}),Lb=Jb(function(a){return typeof a==="string"}),Mb=Jb(function(a){return typeof a==="boolean"}),Nb=Jb(function(a){return typeof a==="bigint"});var Ob=typeof y.BigInt==="function"&&typeof y.BigInt(0)==="bigint";function Pb(a){var b=a;if(Lb(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(Kb(b)&&!Number.isSafeInteger(b))throw Error(String(b));return Ob?BigInt(a):a=Mb(a)?a?"1":"0":Lb(a)?a.trim()||"0":String(a)}
var Qb=Jb(function(a){return Ob?Nb(a):Lb(a)&&/^(?:-?[1-9]\d*|0)$/.test(a)}),Wb=Jb(function(a){return Ob?a>=Rb&&a<=Sb:a[0]==="-"?Tb(a,Ub):Tb(a,Vb)}),Ub=Number.MIN_SAFE_INTEGER.toString(),Rb=Ob?BigInt(Number.MIN_SAFE_INTEGER):void 0,Vb=Number.MAX_SAFE_INTEGER.toString(),Sb=Ob?BigInt(Number.MAX_SAFE_INTEGER):void 0;function Tb(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var D=0,Xb=0;function Yb(a){var b=a>>>0;D=b;Xb=(a-b)/4294967296>>>0}function Zb(a){if(a<0){Yb(0-a);var b=v($b(D,Xb));a=b.next().value;b=b.next().value;D=a>>>0;Xb=b>>>0}else Yb(a)}function ac(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Eb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+bc(c)+bc(a));return c}
function bc(a){a=String(a);return"0000000".slice(a.length)+a}function cc(){var a=D,b=Xb;b&2147483648?Eb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=v($b(a,b)),a=b.next().value,b=b.next().value,a="-"+ac(a,b)):a=ac(a,b);return a}function $b(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};var dc=typeof BigInt==="function"?BigInt.asIntN:void 0,ec=Number.isSafeInteger,fc=Number.isFinite,hc=Math.trunc;function ic(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function jc(a){return a.displayName||a.name||"unknown type name"}function kc(a){if(typeof a!=="boolean")throw Error("Expected boolean but got "+xa(a)+": "+a);return a}var lc=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;
function nc(a){switch(typeof a){case "bigint":return!0;case "number":return fc(a);case "string":return lc.test(a);default:return!1}}function oc(a){return a==null?a:fc(a)?a|0:void 0}function pc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return fc(a)?a|0:void 0}function qc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}
function rc(a){a.indexOf(".");if(qc(a))return a;if(a.length<16)Zb(Number(a));else if(Eb())a=BigInt(a),D=Number(a&BigInt(4294967295))>>>0,Xb=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");Xb=D=0;for(var c=a.length,d=0+b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),Xb*=1E6,D=D*1E6+d,D>=4294967296&&(Xb+=Math.trunc(D/4294967296),Xb>>>=0,D>>>=0);b&&(b=v($b(D,Xb)),a=b.next().value,b=b.next().value,D=a,Xb=b)}return cc()}
function sc(a){nc(a);a=hc(a);if(!ec(a)){Zb(a);var b=D,c=Xb;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);var d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:ac(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function tc(a){nc(a);a=hc(a);if(ec(a))a=String(a);else{var b=String(a);qc(b)?a=b:(Zb(a),a=cc())}return a}function uc(a){return a==null||typeof a==="string"?a:void 0}
function vc(a,b,c,d){if(a!=null&&a[wb]===Fb)return a;if(!Array.isArray(a))return c?d&2?((a=b[rb])||(a=new b,Db(a.D),a=b[rb]=a),b=a):b=new b:b=void 0,b;c=a[C]|0;d=c|d&32|d&2;d!==c&&Bb(a,d);return new b(a)};function wc(a){return a};function xc(){}function yc(a,b){for(var c in a)!isNaN(c)&&b(a,+c,a[c])}function zc(a){var b=new xc;yc(a,function(c,d,e){b[d]=Array.prototype.slice.call(e)});b.g=a.g;return b}function Ac(a,b){b<100||mb(ub,1)};function Bc(a,b,c,d){var e=d!==void 0;d=!!d;var f=Ga(tb),g;!e&&nb&&f&&(g=a[f])&&yc(g,Ac);f=[];var h=a.length;g=4294967295;var l=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var w=h&&a[h-1];w!=null&&typeof w==="object"&&w.constructor===Object?(h--,g=h):w=void 0;if(m&&!(b&128)&&!e){l=!0;var x;g=((x=Cc)!=null?x:wc)(g-n,n,a,w)+n}}b=void 0;for(x=0;x<h;x++){var q=a[x];if(q!=null&&(q=c(q,d))!=null)if(m&&x>=g){var H=x-n,Y=void 0;((Y=b)!=null?Y:b={})[H]=q}else f[x]=q}if(w)for(var la in w)h=w[la],h!=null&&
(h=c(h,d))!=null&&(x=+la,q=void 0,m&&!Number.isNaN(x)&&(q=x+n)<g?f[q]=h:(x=void 0,((x=b)!=null?x:b={})[la]=h));b&&(l?f.push(b):f[g]=b);e&&Ga(tb)&&(a=(c=Ga(tb))?a[c]:void 0)&&a instanceof xc&&(f[tb]=zc(a));return f}
function Dc(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return Wb(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[C]|0;return a.length===0&&b&1?void 0:Bc(a,b,Dc)}if(a!=null&&a[wb]===Fb)return Ec(a);if(a instanceof gb){b=a.g;if(b==null)a="";else if(typeof b==="string")a=b;else{if(db){for(var c="",d=0,e=b.length-10240;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d):
b);b=btoa(c)}else{c===void 0&&(c=0);if(!bb){bb={};d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split("");e=["+/=","+/","-_=","-_.","-_"];for(var f=0;f<5;f++){var g=d.concat(e[f].split(""));ab[f]=g;for(var h=0;h<g.length;h++){var l=g[h];bb[l]===void 0&&(bb[l]=h)}}}c=ab[c];d=Array(Math.floor(b.length/3));e=c[64]||"";for(f=g=0;g<b.length-2;g+=3){var m=b[g],n=b[g+1];l=b[g+2];h=c[m>>2];m=c[(m&3)<<4|n>>4];n=c[(n&15)<<2|l>>6];l=c[l&63];d[f++]=""+h+m+n+l}h=0;l=e;switch(b.length-g){case 2:h=
b[g+1],l=c[(h&15)<<2]||e;case 1:b=b[g],d[f]=""+c[b>>2]+c[(b&3)<<4|h>>4]+l+e}b=d.join("")}a=a.g=b}return a}return}return a}var Fc=fb?structuredClone:function(a){return Bc(a,0,Dc)},Cc;function Ec(a){a=a.D;return Bc(a,a[C]|0,Dc)};function E(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[C]|0;2048&e&&!(2&e)&&Gc();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||Bb(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var l in h)f=+l,f<g&&(c[f+b]=h[l],
delete h[l]);e=e&-8380417|(g&1023)<<13;break a}}if(b){l=Math.max(b,f-(e&128?0:-1));if(l>1024)throw Error("spvt");e=e&-8380417|(l&1023)<<13}}}e|=64;d===0&&(e|=2048);Bb(a,e);return a}function Gc(){mb(vb,5)};function Hc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[C]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=Ic(a,c,!1,b&&!(c&16)):(Cb(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[wb]===Fb)return b=a.D,c=b[C]|0,Gb(a,c)?a:Jc(a,b,c)?Kc(a,b):Ic(b,c);if(a instanceof gb)return a}function Kc(a,b,c){a=new a.constructor(b);c&&(a.g=Hb);a.l=Hb;return a}function Ic(a,b,c,d){d!=null||(d=!!(34&b));a=Bc(a,b,Hc,d);d=32;c&&(d|=2);b=b&8380609|d;Bb(a,b);return a}
function Lc(a){var b=a.D,c=b[C]|0;return Gb(a,c)?Jc(a,b,c)?Kc(a,b,!0):new a.constructor(Ic(b,c,!1)):a}function Mc(a){if(a.g!==Hb)return!1;var b=a.D;b=Ic(b,b[C]|0);Cb(b,2048);a.D=b;a.g=void 0;a.l=void 0;return!0}function Nc(a,b){b===void 0&&(b=a[C]|0);b&32&&!(b&4096)&&Bb(a,b|4096)}function Jc(a,b,c){return c&2?!0:c&32&&!(c&4096)?(Bb(b,c|2),a.g=Hb,!0):!1};var Oc=Pb(0),Pc={};function F(a,b,c,d,e){Object.isExtensible(a);b=Qc(a.D,b,c,e);if(b!==null||d&&a.l!==Hb)return b}function Qc(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}}
function Rc(a,b,c){if(!Mc(a)&&Gb(a,a.D[C]|0))throw Error();var d=a.D;Sc(d,d[C]|0,b,c);return a}function Sc(a,b,c,d){var e=c+-1,f=a.length-1;if(f>=0&&e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;if(d!==void 0){var h;f=((h=b)!=null?h:b=a[C]|0)>>13&1023||536870912;c>=f?d!=null&&(e={},a[f+-1]=(e[c]=d,e)):a[e]=d}return b}
function Tc(a,b,c,d,e,f,g,h){var l=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?Uc(b)||(b|=!a.length||g&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==l&&Bb(a,b),Object.freeze(a)):(f===2&&Uc(b)&&(a=Array.prototype.slice.call(a),l=0,b=Vc(b,d),d=Sc(c,d,e,a)),Uc(b)||(h||(b|=16),b!==l&&Bb(a,b)));2&b||!(4096&b||16&b)||Nc(c,d);return a}function Wc(a,b){a=Qc(a,b);return Array.isArray(a)?a:zb}function Xc(a,b){2&b&&(a|=2);return a|1}function Uc(a){return!!(2&a)&&!!(4&a)||!!(256&a)}
function Yc(a){return a==null?a:typeof a==="string"?a?new gb(a,eb):hb():a.constructor===gb?a:cb&&a!=null&&a instanceof Uint8Array?a.length?new gb(new Uint8Array(a),eb):hb():void 0}function Zc(a,b,c){return $c(a,b)===c?c:-1}
function $c(a,b){a=a.D;if(nb){var c;var d=(c=a[sb])!=null?c:a[sb]=new Map}else sb in a?d=a[sb]:(c=new Map,Object.defineProperty(a,sb,{value:c}),d=c);c=d;d=void 0;var e=c.get(b);if(e==null){for(var f=e=0;f<b.length;f++){var g=b[f];Qc(a,g)!=null&&(e!==0&&(d=Sc(a,d,e)),e=g)}c.set(b,e)}return e}function ad(a,b,c,d){var e=!1;d=Qc(a,d,void 0,function(f){var g=vc(f,c,!1,b);e=g!==f&&g!=null;return g});if(d!=null)return e&&!Gb(d)&&Nc(a,b),d}
function bd(a,b,c){a=a.D;(c=ad(a,a[C]|0,b,c))||(c=b[rb])||(c=new b,Db(c.D),c=b[rb]=c);return c}function G(a,b,c){var d=a.D,e=d[C]|0;b=ad(d,e,b,c);if(b==null)return b;e=d[C]|0;if(!Gb(a,e)){var f=Lc(b);f!==b&&(Mc(a)&&(d=a.D,e=d[C]|0),b=f,e=Sc(d,e,c,b),Nc(d,e))}return b}
function cd(a,b,c){var d=void 0===Ib?2:4,e=a.D,f=e;e=e[C]|0;var g=Gb(a,e),h=g?1:d;d=h===3;var l=!g;(h===2||l)&&Mc(a)&&(f=a.D,e=f[C]|0);a=Wc(f,c);var m=a===zb?7:a[C]|0,n=Xc(m,e);if(g=!(4&n)){var w=a,x=e,q=!!(2&n);q&&(x|=2);for(var H=!q,Y=!0,la=0,pb=0;la<w.length;la++){var mc=vc(w[la],b,!1,x);if(mc instanceof b){if(!q){var ii=Gb(mc);H&&(H=!ii);Y&&(Y=ii)}w[pb++]=mc}}pb<la&&(w.length=pb);n|=4;n=Y?n&-4097:n|4096;n=H?n|8:n&-9}n!==m&&(Bb(a,n),2&n&&Object.freeze(a));if(l&&!(8&n||!a.length&&(h===1||(h!==4?
0:2&n||!(16&n)&&32&e)))){Uc(n)&&(a=Array.prototype.slice.call(a),n=Vc(n,e),e=Sc(f,e,c,a));b=a;l=n;for(m=0;m<b.length;m++)w=b[m],n=Lc(w),w!==n&&(b[m]=n);l|=8;n=l=b.length?l|4096:l&-4097;Bb(a,n)}return a=Tc(a,n,f,e,c,h,g,d)}function dd(a,b,c,d){if(d!=null){if(!(d instanceof b))throw Error("Expected instanceof "+jc(b)+" but got "+(d&&jc(d.constructor)));}else d=void 0;Rc(a,c,d);d&&!Gb(d)&&Nc(a.D);return a}function Vc(a,b){return a=(2&b?a|2:a&-3)&-273}
function ed(a,b){var c=c===void 0?!1:c;a=F(a,b);a=a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0;return a!=null?a:c}function fd(a,b,c){c=c===void 0?0:c;var d;return(d=pc(F(a,b)))!=null?d:c}function gd(a,b){var c=c===void 0?Oc:c;a=F(a,b);b=typeof a;a!=null&&(b==="bigint"?a=Pb(dc(64,a)):nc(a)?b==="string"?(b=hc(Number(a)),ec(b)?a=Pb(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Eb()?Pb(dc(64,BigInt(a))):Pb(rc(a)))):a=ec(a)?Pb(sc(a)):Pb(tc(a)):a=void 0);return a!=null?a:c}
function hd(a,b){var c=c===void 0?"":c;var d;return(d=uc(F(a,b)))!=null?d:c}function id(a,b){var c=c===void 0?0:c;var d;return(d=oc(F(a,b)))!=null?d:c}function jd(a,b){return uc(F(a,b,void 0,Pc))}function kd(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return Rc(a,b,c)}function ld(a,b){if(b!=null){if(!fc(b))throw a=Error("enum"),jb(a,"severity","warning"),a;b|=0}return Rc(a,1,b)};function I(a,b,c){this.D=E(a,b,c)}I.prototype.toJSON=function(){return Ec(this)};function md(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");Cb(b,32);return new a(b)}I.prototype[wb]=Fb;I.prototype.toString=function(){return this.D.toString()};
function nd(a,b){if(b==null)b=a.constructor,(a=b[rb])||(a=new b,Db(a.D),a=b[rb]=a),b=a;else{a=a.constructor;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();b=new a(Db(b))}return b};function od(a){return function(b){return md(a,b)}};function pd(a){this.D=E(a)}u(pd,I);pd.prototype.getTypeName=function(){return hd(this,1).split("/").pop()};var qd=function(a){return Jb(function(b){return b instanceof a&&!Gb(b)})}(pd);function rd(){this.key="45681191";this.defaultValue=!1;this.flagNameForDebugging=void 0}rd.prototype.ctor=function(a){return typeof a==="boolean"?a:this.defaultValue};function sd(){var a=td("[]"),b=ud;this.key="45696263";this.defaultValue=a;this.g=b;this.flagNameForDebugging=void 0}
sd.prototype.ctor=function(a){if(typeof a==="string"&&a)return md(this.g,a);if(!qd(a))return this.defaultValue;var b;try{var c,d=this.g,e=(c=a.getTypeName())!=null?c:"";if(hd(a,1).split("/").pop()!=e)var f=null;else{var g=typeof d==="function"?d:d.constructor,h=a.D,l=h[C]|0,m=Qc(h,2);Mc(a)&&(h=a.D,l=h[C]|0);a=h;if(m!=null&&!(Array.isArray(m)||m!=null&&m[wb]===Fb))throw Error("saw an invalid value of type '"+xa(m)+"' in the Any.value field");var n=vc(m,g,!0,l);if(!(n instanceof g))throw Error("incorrect type in any value: got "+
n.constructor.displayName+", expected "+g.displayName);(g=!!(2&l))||(n=Lc(n));m!==n&&(Sc(a,l,2,n),g||Nc(a));f=n}}catch(w){f=null}return(b=f)!=null?b:this.defaultValue};function vd(a){this.D=E(a)}u(vd,I);var wd=[1];function xd(a){this.D=E(a)}u(xd,I);var yd=[2,3,4,5,6,8];function zd(a){this.D=E(a)}u(zd,I);zd.prototype.j=function(){var a=F(this,3,void 0,void 0,Yc);return a==null?hb():a};function Ad(a){this.D=E(a)}u(Ad,I);var Bd=od(Ad);function ud(a){this.D=E(a)}u(ud,I);var td=od(ud);function Cd(a,b){this.J=a|0;this.H=b|0}function Dd(a){return a.H*4294967296+(a.J>>>0)}r=Cd.prototype;r.isSafeInteger=function(){var a=this.H>>21;return a==0||a==-1&&!(this.J==0&&this.H==-2097152)};
r.toString=function(a){a=a||10;if(a<2||36<a)throw Error("radix out of range: "+a);if(this.isSafeInteger()){var b=Dd(this);return a==10?""+b:b.toString(a)}b=14-(a>>2);var c=Math.pow(a,b),d=J(c,c/4294967296);c=this.div(d);var e=Math,f=e.abs;d=c.multiply(d);d=this.add(Ed(d));e=f.call(e,Dd(d));f=a==10?""+e:e.toString(a);f.length<b&&(f="0000000000000".slice(f.length-b)+f);e=Dd(c);return(a==10?e:e.toString(a))+f};function Fd(a){return a.J==0&&a.H==0}r.R=function(){return this.J^this.H};
r.equals=function(a){return this.J==a.J&&this.H==a.H};r.compare=function(a){return this.H==a.H?this.J==a.J?0:this.J>>>0>a.J>>>0?1:-1:this.H>a.H?1:-1};function Ed(a){var b=~a.J+1|0;return J(b,~a.H+!b|0)}r.add=function(a){var b=this.H>>>16,c=this.H&65535,d=this.J>>>16,e=a.H>>>16,f=a.H&65535,g=a.J>>>16;a=(this.J&65535)+(a.J&65535);g=(a>>>16)+(d+g);d=g>>>16;d+=c+f;return J((g&65535)<<16|a&65535,((d>>>16)+(b+e)&65535)<<16|d&65535)};
r.multiply=function(a){if(Fd(this))return this;if(Fd(a))return a;var b=this.H>>>16,c=this.H&65535,d=this.J>>>16,e=this.J&65535,f=a.H>>>16,g=a.H&65535,h=a.J>>>16;a=a.J&65535;var l=e*a;var m=(l>>>16)+d*a;var n=m>>>16;m=(m&65535)+e*h;n+=m>>>16;n+=c*a;var w=n>>>16;n=(n&65535)+d*h;w+=n>>>16;n=(n&65535)+e*g;w=w+(n>>>16)+(b*a+c*h+d*g+e*f)&65535;return J((m&65535)<<16|l&65535,w<<16|n&65535)};
r.div=function(a){if(Fd(a))throw Error("division by zero");if(this.H<0){if(this.equals(Gd)){if(a.equals(Hd)||a.equals(Id))return Gd;if(a.equals(Gd))return Hd;var b=this.H;b=J(this.J>>>1|b<<31,b>>1);b=b.div(a).shiftLeft(1);if(b.equals(Jd))return a.H<0?Hd:Id;var c=a.multiply(b);c=this.add(Ed(c));return b.add(c.div(a))}return a.H<0?Ed(this).div(Ed(a)):Ed(Ed(this).div(a))}if(Fd(this))return Jd;if(a.H<0)return a.equals(Gd)?Jd:Ed(this.div(Ed(a)));b=Jd;for(c=this;c.compare(a)>=0;){var d=Math.max(1,Math.floor(Dd(c)/
Dd(a))),e=Math.ceil(Math.log(d)/Math.LN2);e=e<=48?1:Math.pow(2,e-48);for(var f=Kd(d),g=f.multiply(a);g.H<0||g.compare(c)>0;)d-=e,f=Kd(d),g=f.multiply(a);Fd(f)&&(f=Hd);b=b.add(f);c=c.add(Ed(g))}return b};r.and=function(a){return J(this.J&a.J,this.H&a.H)};r.or=function(a){return J(this.J|a.J,this.H|a.H)};r.xor=function(a){return J(this.J^a.J,this.H^a.H)};r.shiftLeft=function(a){a&=63;if(a==0)return this;var b=this.J;return a<32?J(b<<a,this.H<<a|b>>>32-a):J(0,b<<a-32)};
function Kd(a){return a>0?a>=0x7fffffffffffffff?Ld:new Cd(a,a/4294967296):a<0?a<=-0x7fffffffffffffff?Gd:Ed(new Cd(-a,-a/4294967296)):Jd}function J(a,b){return new Cd(a,b)}var Jd=J(0,0),Hd=J(1,0),Id=J(-1,-1),Ld=J(4294967295,2147483647),Gd=J(0,2147483648);function Md(a,b){b=b===void 0?window:b;b=b===void 0?window:b;return(b=b.WIZ_global_data)&&a in b?b[a]:null};var Nd;
function Od(){var a=Md("TSDtV",window);a.indexOf("%.@.");a=Bd("["+a.substring(4));if(a=cd(a,zd,1)[0])for(var b=v(cd(a,xd,2)),c=b.next();!c.done;c=b.next()){c=c.value;var d=c.D;if(ad(d,d[C]|0,pd,Zc(c,yd,6))!==void 0)throw Error();}if(a)for(b={},c=v(cd(a,xd,2)),d=c.next();!d.done;d=c.next()){var e=d.value;d=gd(e,1).toString();switch($c(e,yd)){case 3:b[d]=ed(e,Zc(e,yd,3));break;case 2:var f=gd(e,Zc(e,yd,2));Qb(f);Wb(f);f=Wb(f)?Number(f):String(f);b[d]=f;break;case 4:f=void 0;var g=e;var h=Zc(e,yd,4);
e=void 0;e=e===void 0?0:e;g=(f=F(g,h,void 0,void 0,ic))!=null?f:e;b[d]=g;break;case 5:b[d]=hd(e,Zc(e,yd,5));break;case 6:b[d]=G(e,pd,Zc(e,yd,6));break;case 8:f=bd(e,vd,Zc(e,yd,8));switch($c(f,wd)){case 1:b[d]=hd(f,Zc(f,wd,1));break;default:throw Error("case "+$c(f,wd));}break;default:throw Error("case "+$c(e,yd));}}else b={};this.g=b;this.l=a?a.j():null}function Pd(a){var b=Nd=Nd||new Od;return a.key in b.g?a.ctor(b.g[a.key]):a.defaultValue}Od.prototype.j=p("l");function Qd(a){this.D=E(a)}u(Qd,I);var Rd=new sd;var Sd=new rd;function Td(a){this.D=E(a)}u(Td,I);var Ud=function(a){return function(){var b;(b=a[rb])||(b=new a,Db(b.D),b=a[rb]=b);return b}}(Td);Object.create(null);function K(){}K.prototype.equals=function(a){return L(this,a)};K.prototype.R=function(){return Vd(this)};K.prototype.toString=function(){return M(Wd(Xd(Yd(this))))+"@"+M((this.R()>>>0).toString(16))};function Zd(a){return a!=null}K.prototype.s=["java.lang.Object",0];function $d(){}u($d,K);function ae(a,b){a.j=b;be(a)}function N(a,b){a.g=b;ce(b,a)}function be(a){de(a.g)&&(Error.captureStackTrace?Error.captureStackTrace(O(a.g,de,ee)):O(a.g,de,ee).stack=Error().stack)}$d.prototype.toString=function(){var a=Wd(Xd(Yd(this))),b=this.j;return b==null?a:M(a)+": "+M(b)};function fe(a){if(a!=null){var b=a.cb;if(b!=null)return b}a instanceof TypeError?b=ge():(b=new he,be(b),N(b,Error(b)));b.j=a==null?"null":a.toString();N(b,a);return b}
function ie(a){return a instanceof $d}$d.prototype.s=["java.lang.Throwable",0];function je(){}u(je,$d);je.prototype.s=["java.lang.Exception",0];function P(){}u(P,je);P.prototype.s=["java.lang.RuntimeException",0];function ke(){}u(ke,P);function le(){var a=new ke;be(a);N(a,Error(a));return a}function me(a){var b=new ke;ae(b,a);N(b,Error(b));return b}ke.prototype.s=["java.lang.IndexOutOfBoundsException",0];function L(a,b){return Object.is(a,b)||a==null&&b==null};var ne;function oe(){oe=k();for(var a=pe(),b=0;b<256;b=b+1|0)qe(a,b,re(b-128|0));ne=a};function se(){}u(se,P);se.prototype.s=["java.lang.ArithmeticException",0];function te(){}u(te,P);te.prototype.s=["java.lang.ArrayStoreException",0];function ue(){}u(ue,P);ue.prototype.s=["java.lang.ClassCastException",0];function ve(){}u(ve,P);function we(a){var b=new ve;ae(b,a);N(b,Error(b));return b}ve.prototype.s=["java.lang.IllegalArgumentException",0];function xe(){}u(xe,P);function ye(){var a=new xe;be(a);N(a,Error(a));return a}xe.prototype.s=["java.lang.IllegalStateException",0];function he(){}u(he,P);he.prototype.s=["java.lang.JsException",0];function ze(){}u(ze,he);function ge(){var a=new ze;be(a);N(a,new TypeError(a));return a}ze.prototype.s=["java.lang.NullPointerException",0];function Ae(){}u(Ae,ke);Ae.prototype.s=["java.lang.StringIndexOutOfBoundsException",0];function Be(){}u(Be,P);function Ce(){var a=new Be;be(a);N(a,Error(a));return a}Be.prototype.s=["java.util.ConcurrentModificationException",0];function De(){}u(De,P);function Ee(){var a=new De;be(a);N(a,Error(a));return a}De.prototype.s=["java.util.NoSuchElementException",0];function Fe(){}var Ge;u(Fe,K);Fe.prototype.s=["java.lang.Number",0];function He(){}u(He,Fe);He.prototype.s=["java.lang.Double",0];function Ie(a){return Kd(a)}function Je(a){if(!isFinite(a))throw a=new se,be(a),N(a,Error(a)),a.g;return a|0}function Ke(a){return Math.max(Math.min(a,2147483647),-2147483648)|0};function O(a,b,c){a==null||b(a)||(b=M(Wd(Le(a)))+" cannot be cast to "+M(Wd(Xd(c))),Me(b));return a};function Yd(a){return a.constructor}function Ne(a,b,c){if(Object.prototype.hasOwnProperty.call(a.prototype,b))return a.prototype[b];c=c();return a.prototype[b]=c};function Oe(){}u(Oe,K);Oe.prototype.s=["java.lang.Boolean",0];function Pe(a){switch(Q(typeof a)){case "string":for(var b=0,c=0;c<a.length;c=c+1|0){b=(b<<5)-b;var d=a,e=c;Qe(e,d.length);b=b+d.charCodeAt(e)|0}return b;case "number":return a=Q(a),Ke(a);case "boolean":return Q(a)?1231:1237;default:return a==null?0:Vd(a)}}var Re=0;function Vd(a){return a.Ga||(Object.defineProperties(a,{Ga:{value:Re=Re+1|0,enumerable:!1}}),a.Ga)};function Se(a,b){return a.equals?a.equals(b):Object.is(a,b)}function Te(a){return a.R?a.R():Pe(a)}function Le(a){switch(Q(typeof a)){case "number":return Xd(He);case "boolean":return Xd(Oe);case "string":return Xd(Ue);case "function":return Xd(Ve)}if(a instanceof K)a=Xd(Yd(a));else if(Array.isArray(a))a=(a=a.T)?Xd(a.fa,a.ea):Xd(K,1);else if(a!=null)a=Xd(We);else throw new TypeError("null.getClass()");return a};function Ve(){}Ve.prototype.s=["<native function>",1];function We(){}u(We,K);We.prototype.s=["<native object>",0];function Xe(){}u(Xe,P);function R(){var a=new Xe;be(a);N(a,Error(a));return a}Xe.prototype.s=["java.lang.UnsupportedOperationException",0];function S(a,b){return L(a,b)||a!=null&&Se(a,b)}function Ye(a){return a!=null?Te(a):0}function Ze(a){if(a==null)throw ge().g;};function $e(){this.g=0}u($e,Fe);function af(a){a>-129&&a<128?(oe(),a=ne[a+128|0]):a=re(a);return a}function re(a){var b=new $e;b.g=a;return b}$e.prototype.equals=function(a){return bf(a)&&O(a,bf,$e).g==this.g};$e.prototype.R=p("g");$e.prototype.toString=function(){return""+this.g};function bf(a){return a instanceof $e}$e.prototype.s=["java.lang.Integer",0];function cf(){}u(cf,K);r=cf.prototype;r.add=function(){throw R().g;};r.ua=function(a){Q(a);var b=!1;for(a=a.F();a.g();){var c=a.j();b=!!(+b|+this.add(c))}};r.clear=function(){for(var a=this.F();a.g();)a.j(),a.l()};r.contains=function(a){return df(this,a,!1)};r.wa=function(a){Q(a);for(a=a.F();a.g();){var b=a.j();if(!this.contains(b))return!1}return!0};r.remove=function(a){return df(this,a,!0)};r.removeAll=function(a){Q(a);for(var b=!1,c=this.F();c.g();){var d=c.j();a.contains(d)&&(c.l(),b=!0)}return b};
r.da=function(){return ef(this,Array(this.size()))};r.ia=function(a){return ef(this,a)};r.toString=function(){for(var a=ff("[","]"),b=this.F();b.g();){var c=b.j();gf(a,L(c,this)?"(this Collection)":M(c))}return a.toString()};function df(a,b,c){for(a=a.F();a.g();){var d=a.j();if(S(b,d))return c&&a.l(),!0}return!1}r.Ra=function(){return this.da()};r.s=["java.util.AbstractCollection",0];function hf(){}function jf(){var a=new kf;a.l=1;a.j=1;return T(a,lf)}function mf(a){return nf(a.slice(0,a.length))}function T(){return nf(sa.apply(0,arguments))}function of(a){return a!=null&&!!a.ka}hf.prototype.ka=!0;hf.prototype.s=["java.util.List",1];function pf(){}u(pf,cf);r=pf.prototype;r.add=function(a){this.oa(this.size(),a);return!0};r.oa=function(){throw R().g;};r.va=function(a,b){Q(b);for(b=b.F();b.g();){var c=b.j(),d=void 0;this.oa((d=a,a=a+1|0,d),c)}};r.clear=function(){this.Pa(0,this.size())};r.equals=function(a){if(L(a,this))return!0;if(!of(a))return!1;a=O(a,of,hf);if(this.size()!=a.size())return!1;a=a.F();for(var b=this.F();b.g();){var c=b.j(),d=a.j();if(!S(c,d))return!1}return!0};
r.R=function(){qf();for(var a=1,b=this.F();b.g();){var c=b.j();a=Math.imul(31,a)+Ye(c)|0}return a};r.indexOf=function(a){for(var b=0,c=this.size();b<c;b=b+1|0)if(S(a,this.W(b)))return b;return-1};r.F=function(){var a=new rf;a.C=this;a.o=0;a.v=-1;return a};r.lastIndexOf=function(a){for(var b=this.size()-1|0;b>-1;b=b-1|0)if(S(a,this.W(b)))return b;return-1};r.Ba=function(a){var b=new sf;b.C=this;b.o=0;b.v=-1;tf(a,this.size());b.o=a;return b};r.Da=function(){throw R().g;};
r.Pa=function(a,b){for(var c=this.Ba(a);a<b;a=a+1|0)c.j(),c.l()};r.ka=!0;r.s=["java.util.AbstractList",0];function uf(){}u(uf,pf);r=uf.prototype;r.ua=function(a){this.va(this.g.length,a)};r.contains=function(a){return this.indexOf(a)!=-1};r.W=function(a){vf(a,this.g.length);return this.g[a]};r.indexOf=function(a){a:{for(var b=0,c=this.g.length;b<c;b=b+1|0)if(S(a,this.g[b])){a=b;break a}a=-1}return a};r.F=function(){var a=new wf;a.C=this;a.o=0;a.v=-1;return a};r.lastIndexOf=function(a){a:{for(var b=this.g.length-1|0;b>=0;b=b-1|0)if(S(a,this.g[b])){a=b;break a}a=-1}return a};
r.Da=function(a){this.W(a);this.g.splice(a,1)};r.remove=function(a){a=this.indexOf(a);if(a==-1)return!1;this.g.splice(a,1);return!0};r.size=function(){return this.g.length};r.ia=function(a){var b=this.g.length;a.length<b&&(a=xf(Array(b),a));for(var c=0;c<b;c=c+1|0)qe(a,c,this.g[c]);a.length>b&&qe(a,b,null);return a};r.ka=!0;r.s=["java.util.ArrayListBase",0];function yf(){}u(yf,uf);function zf(){var a=new yf;a.g=[];return a}r=yf.prototype;r.add=function(a){this.g.push(a);return!0};r.oa=function(a,b){tf(a,this.g.length);this.g.splice(a,0,b)};r.va=function(a,b){tf(a,this.g.length);b=b.da();var c=b.length;if(c!=0){var d=this.g.length+c|0;this.g.length=d;var e=a+c|0;Af(this.g,a,this.g,e,d-e|0);Af(b,0,this.g,a,c)}};r.da=function(){var a=this.g,b=a.slice();b.T=a.T;b==null||Bf(b,K,Zd,1)||(a=Xd(K,1),a=Wd(Le(b))+" cannot be cast to "+Wd(a),Me(a));return b};
r.Pa=function(a,b){var c=this.g.length;if(a<0||b>c)throw me("fromIndex: "+a+", toIndex: "+b+", size: "+c).g;if(a>b)throw we("fromIndex: "+a+" > toIndex: "+b).g;this.g.splice(a,b-a|0)};r.s=["java.util.ArrayList",0];function wf(){this.v=this.o=0}u(wf,K);wf.prototype.g=function(){return this.o<this.C.g.length};wf.prototype.j=function(){Cf(this.g());var a;this.v=(a=this.o,this.o=this.o+1|0,a);return this.C.g[this.v]};wf.prototype.l=function(){Df(this.v!=-1);var a=this.C,b=this.o=this.v;a.g.splice(b,1);this.v=-1};wf.prototype.s=["java.util.ArrayListBase$1",0];function Ef(){}u(Ef,pf);r=Ef.prototype;r.contains=ba(!1);r.W=function(a){vf(a,0);return null};r.F=function(){return Ff()};r.size=ba(0);r.s=["java.util.Collections$EmptyList",0];function Gf(){}var Hf;u(Gf,K);Gf.prototype.g=ba(!1);Gf.prototype.j=function(){throw Ee().g;};Gf.prototype.l=function(){throw ye().g;};function If(){If=k();Hf=new Gf}Gf.prototype.s=["java.util.Collections$EmptyListIterator",0];function Jf(){}u(Jf,K);Jf.prototype.g=function(){return this.o.g()};Jf.prototype.j=function(){return O(this.o.j(),U,V).N()};Jf.prototype.l=function(){this.o.l()};Jf.prototype.s=["java.util.AbstractMap$1$1",0];function V(){}function U(a){return a!=null&&!!a.ta}V.prototype.ta=!0;V.prototype.s=["java.util.Map$Entry",1];function Kf(){}function Lf(){var a=sa.apply(0,arguments);qf();if(a.length==0)a=Mf(Nf);else{var b=new Of;b.g=Pf();for(var c=0;c<a.length;c=c+1|0)if(!b.add(Q(a[c])))throw we("Duplicate element").g;a=Mf(b)}return a}function Qf(a){return a!=null&&!!a.la}Kf.prototype.la=!0;Kf.prototype.s=["java.util.Set",1];function Rf(){}u(Rf,cf);r=Rf.prototype;r.equals=function(a){if(L(a,this))return!0;if(!Qf(a))return!1;a=O(a,Qf,Kf);return a.size()!=this.size()?!1:this.wa(a)};r.R=function(){return Sf(this)};r.removeAll=function(a){Q(a);var b=this.size();if(b<a.size())for(var c=this.F();c.g();){var d=c.j();a.contains(d)&&c.l()}else for(a=a.F();a.g();)c=a.j(),this.remove(c);return b!=this.size()};r.la=!0;r.s=["java.util.AbstractSet",0];function Tf(){}u(Tf,Rf);r=Tf.prototype;r.clear=function(){this.g.clear()};r.contains=function(a){return this.g.ga(a)};r.F=function(){var a=this.g.Y().F(),b=new Jf;b.o=a;return b};r.remove=function(a){return this.g.ga(a)?(this.g.remove(a),!0):!1};r.size=function(){return this.g.size()};r.s=["java.util.AbstractMap$1",0];function Uf(){}u(Uf,K);Uf.prototype.g=function(){return this.o.g()};Uf.prototype.j=function(){return O(this.o.j(),U,V).P()};Uf.prototype.l=function(){this.o.l()};Uf.prototype.s=["java.util.AbstractMap$2$1",0];function Vf(){}u(Vf,cf);r=Vf.prototype;r.clear=function(){this.g.clear()};r.contains=function(a){return this.g.Ia(a)};r.F=function(){var a=this.g.Y().F(),b=new Uf;b.o=a;return b};r.size=function(){return this.g.size()};r.s=["java.util.AbstractMap$2",0];function Wf(){}u(Wf,K);r=Wf.prototype;r.N=p("j");r.P=p("g");r.Ha=function(a){var b=this.g;this.g=a;return b};r.equals=function(a){if(!U(a))return!1;a=O(a,U,V);return S(this.j,a.N())&&S(this.g,a.P())};r.R=function(){return Ye(this.j)^Ye(this.g)};r.toString=function(){return M(this.j)+"="+M(this.g)};r.ta=!0;r.s=["java.util.AbstractMap$AbstractEntry",0];function Xf(){}u(Xf,Wf);function Yf(a,b){var c=new Xf;c.j=a;c.g=b;return c}Xf.prototype.s=["java.util.AbstractMap$SimpleEntry",0];function Zf(){}function $f(a){return a!=null&&!!a.Fa}Zf.prototype.Fa=!0;Zf.prototype.s=["java.util.Map",1];function ag(){}u(ag,K);r=ag.prototype;r.clear=function(){this.Y().clear()};r.ga=function(a){return bg(this,a,!1)!=null};r.Ia=function(a){for(var b=this.Y().F();b.g();){var c=O(b.j(),U,V).P();if(S(a,c))return!0}return!1};function cg(a,b){var c=b.N();b=b.P();var d=a.get(c);return!S(b,d)||d==null&&!a.ga(c)?!1:!0}r.equals=function(a){if(L(a,this))return!0;if(!$f(a))return!1;a=O(a,$f,Zf);if(this.size()!=a.size())return!1;for(a=a.Y().F();a.g();){var b=O(a.j(),U,V);if(!cg(this,b))return!1}return!0};
r.get=function(a){return dg(bg(this,a,!1))};r.R=function(){return Sf(this.Y())};r.Oa=function(){var a=new Tf;a.g=this;return a};r.X=function(){throw R().g;};r.remove=function(a){return dg(bg(this,a,!0))};r.size=function(){return this.Y().size()};r.toString=function(){for(var a=ff("{","}"),b=this.Y().F();b.g();){var c=O(b.j(),U,V);c=M(eg(this,c.N()))+"="+M(eg(this,c.P()));gf(a,c)}return a.toString()};function eg(a,b){return L(b,a)?"(this Map)":M(b)}r.values=function(){var a=new Vf;a.g=this;return a};
function dg(a){return a==null?null:a.P()}function bg(a,b,c){for(a=a.Y().F();a.g();){var d=O(a.j(),U,V);if(S(b,d.N()))return c&&(d=Yf(d.N(),d.P()),a.l()),d}return null}r.Fa=!0;r.s=["java.util.AbstractMap",0];function fg(){}u(fg,K);fg.prototype.toString=p("g");fg.prototype.s=["java.lang.AbstractStringBuilder",0];function gg(){}u(gg,fg);gg.prototype.s=["java.lang.StringBuilder",0];function hg(){}u(hg,K);function ff(a,b){var c=new hg;c.o=", ".toString();c.l=a.toString();c.j=b.toString();c.v=M(c.l)+M(c.j);return c}function gf(a,b){if(a.g==null){var c=new gg,d=O(Q(a.l),ig,Ue);c.g=d;a.g=c}else c=a.g,c.g=M(c.g)+M(a.o);a=a.g;a.g=M(a.g)+M(b)}hg.prototype.toString=function(){return this.g==null?this.v:this.j.length==0?this.g.toString():M(this.g.toString())+M(this.j)};hg.prototype.s=["java.util.StringJoiner",0];function jg(){}u(jg,Rf);jg.prototype.contains=ba(!1);jg.prototype.F=function(){return Ff()};jg.prototype.size=ba(0);jg.prototype.s=["java.util.Collections$EmptySet",0];function kg(){}u(kg,K);r=kg.prototype;r.add=function(){throw R().g;};r.ua=function(){throw R().g;};r.clear=function(){throw R().g;};r.contains=function(a){return this.g.contains(a)};r.wa=function(a){return this.g.wa(a)};r.F=function(){var a=this.g.F(),b=new lg;b.o=a;return b};r.remove=function(){throw R().g;};r.removeAll=function(){throw R().g;};r.size=function(){return this.g.size()};r.da=function(){return this.g.da()};r.ia=function(a){return this.g.ia(a)};r.toString=function(){return this.g.toString()};
r.Ra=function(){return this.da()};r.s=["java.util.Collections$UnmodifiableCollection",0];function lg(){}u(lg,K);lg.prototype.g=function(){return this.o.g()};lg.prototype.j=function(){return this.o.j()};lg.prototype.l=function(){throw R().g;};lg.prototype.s=["java.util.Collections$UnmodifiableCollectionIterator",0];function mg(){}u(mg,kg);r=mg.prototype;r.oa=function(){throw R().g;};r.va=function(){throw R().g;};r.equals=function(a){return Se(this.j,a)};r.W=function(a){return this.j.W(a)};r.R=function(){return Te(this.j)};r.indexOf=function(a){return this.j.indexOf(a)};r.lastIndexOf=function(a){return this.j.lastIndexOf(a)};r.Ba=function(a){a=this.j.Ba(a);var b=new ng;b.o=a;return b};r.Da=function(){throw R().g;};r.ka=!0;r.s=["java.util.Collections$UnmodifiableList",0];function ng(){}u(ng,lg);ng.prototype.s=["java.util.Collections$UnmodifiableListIterator",0];function og(){}u(og,kg);og.prototype.equals=function(a){return Se(this.g,a)};og.prototype.R=function(){return Te(this.g)};og.prototype.la=!0;og.prototype.s=["java.util.Collections$UnmodifiableSet",0];function pg(){}u(pg,mg);pg.prototype.s=["java.util.Collections$UnmodifiableRandomAccessList",0];var qg,Nf;function Ff(){qf();return If(),Hf}function nf(a){qf();for(var b=0;b<a.length;b=b+1|0)Q(a[b]);a.length==0?b=qg:(b=new rg,Q(a),b.g=a);a=new pg;a.g=b;a.j=b;return a}function Mf(a){qf();var b=new og;b.g=a;return b}function Sf(a){qf();var b=0;for(a=a.F();a.g();){var c=a.j();b=b+Ye(c)|0}return b}function qf(){qf=k();qg=new Ef;Nf=new jg};function sg(){}u(sg,Rf);r=sg.prototype;r.clear=function(){this.g.clear()};r.contains=function(a){return U(a)?cg(this.g,O(a,U,V)):!1};r.F=function(){var a=new tg;a.o=this.g;a.G=a.o.l.F();a.v=a.G;a.C=ug(a);a.A=a.o.j;return a};r.remove=function(a){return this.contains(a)?(a=O(a,U,V).N(),this.g.remove(a),!0):!1};r.size=function(){return this.g.size()};r.s=["java.util.AbstractHashMap$EntrySet",0];function tg(){this.C=!1;this.A=0}u(tg,K);tg.prototype.g=p("C");function ug(a){if(a.v.g())return!0;if(!L(a.v,a.G))return!1;a.v=a.o.g.F();return a.v.g()}tg.prototype.l=function(){Df(this.B!=null);if(this.o.j!=this.A)throw Ce().g;this.B.l();this.B=null;this.C=ug(this);this.A=this.o.j};tg.prototype.j=function(){if(this.o.j!=this.A)throw Ce().g;Cf(this.g());this.B=this.v;var a=O(this.v.j(),U,V);this.C=ug(this);return a};tg.prototype.s=["java.util.AbstractHashMap$EntrySetIterator",0];function vg(){this.j=0}u(vg,ag);r=vg.prototype;r.clear=function(){wg(this)};function wg(a){var b=new xg;b.j=new Map;b.l=a;a.g=b;b=new yg;b.g=new Map;b.o=a;a.l=b;zg(a)}function zg(a){a.j=a.j+1|0}r.ga=function(a){return ig(a)?this.l.g.has(a):Ag(a,Bg(this.g,a==null?0:Te(a)))!=null};r.Ia=function(a){return Cg(a,this.l)||Cg(a,this.g)};function Cg(a,b){for(b=b.F();b.g();){var c=O(b.j(),U,V),d=a;c=c.P();if(S(d,c))return!0}return!1}r.Y=function(){var a=new sg;a.g=this;return a};
r.get=function(a){return ig(a)?this.l.g.get(a):dg(Ag(a,Bg(this.g,a==null?0:Te(a))))};r.X=function(a,b){if(ig(a))a=Dg(this.l,a,b);else a:{var c=this.g,d=a==null?0:Te(a),e=Bg(c,d);if(e.length==0)c.j.set(d,e);else if(d=Ag(a,e),d!=null){a=d.Ha(b);break a}qe(e,e.length,Yf(a,b));c.g=c.g+1|0;zg(c.l);a=null}return a};r.remove=function(a){return ig(a)?Eg(this.l,a):Fg(this.g,a)};r.size=function(){return this.g.g+this.l.l|0};r.s=["java.util.AbstractHashMap",0];function Gg(){this.o=0}u(Gg,K);Gg.prototype.g=function(){if(this.o<this.v.length)return!0;var a=this.B.next();return a.done?!1:(this.v=a.value[1],this.o=0,!0)};Gg.prototype.l=function(){Fg(this.A,this.C.N());this.o!=0&&(this.o=this.o-1|0)};Gg.prototype.j=function(){var a;return this.C=this.v[a=this.o,this.o=this.o+1|0,a]};Gg.prototype.s=["java.util.InternalHashCodeMap$1",0];function xg(){this.g=0}u(xg,K);function Fg(a,b){for(var c=b==null?0:Te(b),d=Bg(a,c),e=0;e<d.length;e=e+1|0){var f=d[e];if(S(b,f.N()))return d.length==1?(d.length=0,a.j.delete(c)):d.splice(e,1),a.g=a.g-1|0,zg(a.l),f.P()}return null}function Ag(a,b){for(var c=0;c<b.length;c++){var d=b[c];if(S(a,d.N()))return d}return null}xg.prototype.F=function(){var a=new Gg;a.A=this;a.B=a.A.j.entries();a.o=0;a.v=[];a.C=null;return a};function Bg(a,b){a=a.j.get(b);return a==null?[]:a}
xg.prototype.s=["java.util.InternalHashCodeMap",0];function Hg(){}u(Hg,K);Hg.prototype.g=function(){return!this.v.done};Hg.prototype.l=function(){Eg(this.o,this.A.value[0])};Hg.prototype.j=function(){this.A=this.v;this.v=this.C.next();var a=new Ig,b=this.A,c=this.o.j;a.j=this.o;a.g=b;a.l=c;return a};Hg.prototype.s=["java.util.InternalStringMap$1",0];function Jg(){}u(Jg,K);r=Jg.prototype;r.equals=function(a){if(!U(a))return!1;a=O(a,U,V);return S(this.N(),a.N())&&S(this.P(),a.P())};r.R=function(){return Ye(this.N())^Ye(this.P())};r.toString=function(){return M(this.N())+"="+M(this.P())};r.ta=!0;r.s=["java.util.AbstractMapEntry",0];function Ig(){this.l=0}u(Ig,Jg);Ig.prototype.N=function(){return this.g.value[0]};Ig.prototype.P=function(){return this.j.j!=this.l?this.j.g.get(this.g.value[0]):this.g.value[1]};Ig.prototype.Ha=function(a){return Dg(this.j,this.g.value[0],a)};Ig.prototype.s=["java.util.InternalStringMap$2",0];function yg(){this.j=this.l=0}u(yg,K);function Dg(a,b,c){var d=a.g.get(b);a.g.set(b,c===void 0?null:c);d===void 0?(a.l=a.l+1|0,zg(a.o)):a.j=a.j+1|0;return d}function Eg(a,b){var c=a.g.get(b);c===void 0?a.j=a.j+1|0:(a.g.delete(b),a.l=a.l-1|0,zg(a.o));return c}yg.prototype.F=function(){var a=new Hg;a.o=this;a.C=a.o.g.entries();a.v=a.C.next();return a};yg.prototype.s=["java.util.InternalStringMap",0];function Kg(){this.j=0}u(Kg,vg);function Pf(){var a=new Kg;wg(a);return a}Kg.prototype.s=["java.util.HashMap",0];function Of(){}u(Of,Rf);r=Of.prototype;r.add=function(a){return this.g.X(a,this)==null};r.clear=function(){this.g.clear()};r.contains=function(a){return this.g.ga(a)};r.F=function(){return this.g.Oa().F()};r.remove=function(a){return this.g.remove(a)!=null};r.size=function(){return this.g.size()};r.la=!0;r.s=["java.util.HashSet",0];function Lg(){}var Mg;u(Lg,K);function Ng(a){var b=new Lg;b.g=a;return b}Lg.prototype.equals=function(a){if(L(a,this))return!0;if(!Og(a))return!1;a=O(a,Og,Lg);return S(this.g,a.g)};Lg.prototype.R=function(){return Ye(this.g)};Lg.prototype.toString=function(){return this.g!=null?"Optional.of("+M(M(this.g))+")":"Optional.empty()"};function Pg(){Pg=k();Mg=Ng(null)}function Og(a){return a instanceof Lg}Lg.prototype.s=["java.util.Optional",0];function ef(a,b){var c=a.size();b.length<c&&(b=xf(Array(c),b));var d=b;a=a.F();for(var e=0;e<c;e=e+1|0)qe(d,e,a.j());b.length>c&&qe(b,c,null);return b};function rf(){this.v=this.o=0}u(rf,K);rf.prototype.g=function(){return this.o<this.C.size()};rf.prototype.j=function(){Cf(this.g());var a;return this.C.W(this.v=(a=this.o,this.o=this.o+1|0,a))};rf.prototype.l=function(){Df(this.v!=-1);this.C.Da(this.v);this.o=this.v;this.v=-1};rf.prototype.s=["java.util.AbstractList$IteratorImpl",0];function sf(){rf.call(this)}u(sf,rf);sf.prototype.s=["java.util.AbstractList$ListIteratorImpl",0];function rg(){}u(rg,pf);r=rg.prototype;r.contains=function(a){return this.indexOf(a)!=-1};r.W=function(a){var b=this.size();vf(a,b);return this.g[a]};r.size=function(){return this.g.length};r.da=function(){return this.ia(Array(this.g.length))};r.F=function(){var a=new Qg;a.v=this.g;return a};r.ia=function(a){var b=this.g.length;a.length<b&&(a=xf(Array(b),a));for(var c=0;c<b;c=c+1|0)qe(a,c,this.g[c]);a.length>b&&qe(a,b,null);return a};r.s=["java.util.Arrays$ArrayList",0];function Qg(){this.o=0}u(Qg,K);Qg.prototype.g=function(){return this.o<this.v.length};Qg.prototype.j=function(){Cf(this.g());var a;return this.v[a=this.o,this.o=this.o+1|0,a]};Qg.prototype.l=function(){throw R().g;};Qg.prototype.s=["javaemul.internal.ArrayIterator",0];function Rg(a,b){if(L(a,b))return!0;if(a==null||b==null||a.length!=b.length)return!1;for(var c=0;c<a.length;c=c+1|0)if(!S(a[c],b[c]))return!1;return!0};function Sg(){}u(Sg,ve);Sg.prototype.s=["java.lang.NumberFormatException",0];function Af(a,b,c,d,e){var f=a.length,g=c.length;if(b<0||d<0||e<0||(b+e|0)>f||(d+e|0)>g)throw le().g;if(e!=0)if(L(a,c)&&b<d)for(b=b+e|0,e=d+e|0;e>d;)qe(c,e=e-1|0,a[b=b-1|0]);else for(e=d+e|0;d<e;)g=f=void 0,qe(c,(f=d,d=d+1|0,f),a[g=b,b=b+1|0,g])};function xf(a,b){a.T=b.T;return a};function Me(a){var b=new ue;ae(b,a);N(b,Error(b));throw b.g;}function Cf(a){if(!a)throw Ee().g;}function Df(a){if(!a)throw ye().g;}function Q(a){Tg(a);return a}function Tg(a){if(a==null)throw ge().g;return a}function vf(a,b){if(a<0||a>=b)throw me("Index: "+a+", Size: "+b).g;}function Qe(a,b){if(a<0||a>=b){var c=new Ae;ae(c,"Index: "+a+", Size: "+b);N(c,Error(c));throw c.g;}}function tf(a,b){if(a<0||a>b)throw me("Index: "+a+", Size: "+b).g;};function pe(){var a=[256];return Ug(a,Vg($e,bf,a.length))}function Ug(a,b){var c=a[0];if(c==null)return null;var d=new globalThis.Array(c);b&&(d.T=b);if(a.length>1){a=a.slice(1);b=b&&Vg(b.fa,b.Aa,b.ea-1);for(var e=0;e<c;e++)d[e]=Ug(a,b)}else if(b&&(a=b.fa.Cb,a!==void 0))for(b=0;b<c;b++)d[b]=a;return d}function Wg(a){a.T=Vg(Ue,ig,1);return a}
function qe(a,b,c){var d;if(!(d=c==null))a:{if(d=a.T)if(d.ea>1){if(!Bf(c,d.fa,d.Aa,d.ea-1)){d=!1;break a}}else if(c!=null&&!d.Aa(c)){d=!1;break a}d=!0}if(!d)throw a=new te,be(a),N(a,Error(a)),a.g;a[b]=c}function Bf(a,b,c,d){if(a==null||!Array.isArray(a))return!1;a=a.T||{fa:K,ea:1};var e=a.ea;return e==d?(d=a.fa,d===b?!0:b&&b.prototype.Sa||d&&d.prototype.Sa?!1:c(d.prototype)):e>d?K==b:!1}function Vg(a,b,c){return{fa:a,Aa:b,ea:c}};function Ue(){}u(Ue,K);function M(a){return a==null?"null":a.toString()}function Xg(a,b){Qe(b,a.length+1|0);return a.substr(b)}function ig(a){return"string"===typeof a}Ue.prototype.s=["java.lang.String",0];function Yg(){}var Zg,$g;u(Yg,K);function ah(){ah=k();$g=new bh;Zg=new ch}Yg.prototype.s=["java.util.Locale",0];function bh(){}u(bh,Yg);bh.prototype.toString=ba("");bh.prototype.s=["java.util.Locale$1",0];function ch(){}u(ch,Yg);ch.prototype.toString=ba("unknown");ch.prototype.s=["java.util.Locale$4",0];function dh(a,b){this.g=a;this.j=b}u(dh,K);function Xd(a,b){var c=b||0;return Ne(a,"$$class/"+c,function(){return new dh(a,c)})}function Wd(a){return a.j!=0?M(eh("[",a.j))+M(a.g.prototype.s[1]==3?a.g.prototype.s[2]:"L"+M(a.g.prototype.s[0])+";"):a.g.prototype.s[0]}function fh(a){return M(a.g.prototype.s[0])+M(eh("[]",a.j))}function gh(a,b){return Xg(a,a.lastIndexOf(b)+1|0)}
dh.prototype.toString=function(){return String(this.j==0&&this.g.prototype.s[1]==1?"interface ":this.j==0&&this.g.prototype.s[1]==3?"":"class ")+M(Wd(this))};function eh(a,b){for(var c="",d=0;d<b;d=d+1|0)c=M(c)+M(a);return c}dh.prototype.s=["java.lang.Class",0];function ee(){}function de(a){return a instanceof Error}ee.prototype.s=["Error",0];function ce(a,b){if(a instanceof Object)try{a.cb=b,Object.defineProperties(a,{cause:{get:function(){return b.l&&b.l.g}}})}catch(c){}};function hh(a,b){this.l=b;this.j=a;be(this);N(this,Error(this))}u(hh,P);hh.prototype.getMessage=p("j");fa.Object.defineProperties(hh.prototype,{error:{configurable:!0,enumerable:!0,get:function(){var a=Error(),b=this.g;a.fileName=b.fileName;a.lineNumber=b.lineNumber;a.columnNumber=b.columnNumber;a.message=b.message;a.name=b.name;a.stack=b.stack;a.toSource=b.toSource;a.cause=b.cause;for(var c in b)c.indexOf("__java$")!=0&&(a[c]=b[c]);return a}}});
hh.prototype.s=["com.google.apps.docs.xplat.base.XplatException",0];function ih(){}function jh(a){return a instanceof Error}ih.prototype.s=["Error",0];function kh(){var a=a==null?function(c){return Ke(Math.floor(Math.random()*c))}:a;var b=(a(2147483647)>>>0).toString(16);b=M(lh("0",Math.max(0,8-b.length|0)))+M(b);a=(a(2147483647)>>>0).toString(16);return M(a)+M(b)};function mh(){}function nh(a){return a instanceof Array}mh.prototype.s=["Array",0];function oh(){}function ph(a){return a instanceof Object}oh.prototype.s=["Object",0];var lh=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};function qh(){}function W(a){return new RegExp(a,"")}function rh(a){return a instanceof RegExp}qh.prototype.s=["RegExp",0];function sh(){}u(sh,K);function th(a,b){var c=new sh;if(b==null)throw ge().g;c.j=O(b,ig,Ue);b="g";a.multiline&&(b=M(b)+"m");a.ignoreCase&&(b=M(b)+"i");c.l=new RegExp(a.source,b);return c}function uh(a){a.g=a.l.exec(a.j);return a.g!=null}sh.prototype.s=["com.google.apps.xplat.regex.RegExpMatcher",0];var vh={Wb:"build-label",Db:"buildLabel",Eb:"clientLog",Ib:"docId",Yb:"mobile-app-version",jc:"severity",oc:"severity-unprefixed",Rb:"isArrayPrototypeIntact",Sb:"isEditorElementAttached",Lb:"documentCharacterSet",Ub:"isModuleLoadFailure",fc:"reportName",Xb:"locale",Gb:"createdOnServer",cc:"numUnsavedCommands",Hb:"cspViolationContext",ec:"relatedToBrowserExtension",qc:"workerError",Jb:"docosPostLimitExceeded",Kb:"docosPostLimitType",hc:"saveTakingTooLongOnClient",lc:"truncatedCommentNotificationsCount",
mc:"truncatedCommentNotificationsFromPayload",bc:"nonfatalReason"};function wh(){this.g=!1}u(wh,K);r=wh.prototype;r.dispose=function(){this.g||(this.g=!0,this.Ta(),gh(gh(fh(Xd(Yd(this))),"."),"$"))};r.za=p("g");r.Ta=function(){if(this.o!=null){for(var a=this.o,b=0;b<a.length;b++)a[b].dispose();this.o.length=0}};r.toString=function(){return K.prototype.toString.call(this)||""};r.s=["com.google.apps.xplat.disposable.Disposable",0];/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var xh=qa([""]),yh=ra(["\x00"],["\\0"]),zh=ra(["\n"],["\\n"]),Ah=ra(["\x00"],["\\u0000"]);function Bh(a){return a.toString().indexOf("`")===-1}Bh(function(a){return a(xh)})||Bh(function(a){return a(yh)})||Bh(function(a){return a(zh)})||Bh(function(a){return a(Ah)});function Ch(a){var b=y.onerror;y.onerror=function(c,d,e,f,g){b&&b(c,d,e,f,g);a({message:c,fileName:d,line:e,lineNumber:e,sc:f,error:g});return!0}}
function Dh(a){var b=wa("window.location.href");a==null&&(a='Unknown Error of type "null/undefined"');if(typeof a==="string")return{message:a,name:"Unknown error",lineNumber:"Not available",fileName:b,stack:"Not available"};var c=!1;try{var d=a.lineNumber||a.line||"Not available"}catch(f){d="Not available",c=!0}try{var e=a.fileName||a.filename||a.sourceURL||y.$googDebugFname||b}catch(f){e="Not available",c=!0}b=Eh(a);return!c&&a.lineNumber&&a.fileName&&a.stack&&a.message&&a.name?{message:a.message,
name:a.name,lineNumber:a.lineNumber,fileName:a.fileName,stack:b}:(c=a.message,c==null&&(c=a.constructor&&a.constructor instanceof Function?'Unknown Error of type "'+(a.constructor.name?a.constructor.name:Fh(a.constructor))+'"':"Unknown Error of unknown type",typeof a.toString==="function"&&Object.prototype.toString!==a.toString&&(c+=": "+a.toString())),{message:c,name:a.name||"UnknownError",lineNumber:d,fileName:e,stack:b||"Not available"})}
function Eh(a,b){b||(b={});b[Gh(a)]=!0;var c=a.stack||"",d=a.cause;d&&!b[Gh(d)]&&(c+="\nCaused by: ",d.stack&&d.stack.indexOf(d.toString())==0||(c+=typeof d==="string"?d:d.message+"\n"),c+=Eh(d,b));a=a.errors;if(Array.isArray(a)){d=1;var e;for(e=0;e<a.length&&!(d>4);e++)b[Gh(a[e])]||(c+="\nInner error "+d++ +": ",a[e].stack&&a[e].stack.indexOf(a[e].toString())==0||(c+=typeof a[e]==="string"?a[e]:a[e].message+"\n"),c+=Eh(a[e],b));e<a.length&&(c+="\n... "+(a.length-e)+" more inner errors")}return c}
function Gh(a){var b="";typeof a.toString==="function"&&(b=""+a);return b+a.stack}function Hh(a,b){a instanceof Error||(a=Error(a),Error.captureStackTrace&&Error.captureStackTrace(a,Hh));a.stack||(a.stack=Ih(Hh));if(b){for(var c=0;a["message"+c];)++c;a["message"+c]=String(b)}return a}function Jh(a,b){a=Hh(a);if(b)for(var c in b)jb(a,c,b[c]);return a}
function Ih(a){var b=Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||Ih),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=Kh(a||arguments.callee.caller,[]));return b}
function Kh(a,b){var c=[];if(Xa(b,a)>=0)c.push("[...circular reference...]");else if(a&&b.length<50){c.push(Fh(a)+"(");for(var d=a.arguments,e=0;d&&e<d.length;e++){e>0&&c.push(", ");var f=d[e];switch(typeof f){case "object":f=f?"object":"null";break;case "string":break;case "number":f=String(f);break;case "boolean":f=f?"true":"false";break;case "function":f=(f=Fh(f))?f:"[fn]";break;default:f=typeof f}f.length>40&&(f=f.slice(0,40)+"...");c.push(f)}b.push(a);c.push(")\n");try{c.push(Kh(a.caller,b))}catch(g){c.push("[exception trying to get caller]\n")}}else a?
c.push("[...long stack...]"):c.push("[end]");return c.join("")}function Fh(a){if(Lh[a])return Lh[a];a=String(a);if(!Lh[a]){var b=/function\s+([^\(]+)/m.exec(a);Lh[a]=b?b[1]:"[Anonymous]"}return Lh[a]}var Lh={};function Mh(a,b){this.name=a;this.value=b}Mh.prototype.toString=p("name");var Nh=new Mh("SEVERE",1E3),Oh=new Mh("WARNING",900),Ph=new Mh("CONFIG",700);function Qh(){this.clear()}var Rh;function Sh(a){var b=Th(),c=b.g;if(c[0]){var d=b.j;b=b.l?d:-1;do b=(b+1)%0,a(c[b]);while(b!==d)}}Qh.prototype.clear=function(){this.g=[];this.j=-1;this.l=!1};function Th(){Rh||(Rh=new Qh);return Rh};var Uh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function Vh(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}}
function Wh(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]}function Xh(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)Xh(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))}function Yh(a,b){var c=[];for(b=b||0;b<a.length;b+=2)Xh(a[b],a[b+1],c);return c.join("&")}
function Zh(a){var b=[],c;for(c in a)Xh(c,a[c],b);return b.join("&")}function $h(a,b){var c=arguments.length==2?Yh(arguments[1],0):Yh(arguments,1);return Wh(a,c)};function ai(a){a&&typeof a.dispose=="function"&&a.dispose()};function bi(a){for(var b=0,c=arguments.length;b<c;++b){var d=arguments[b];ya(d)?bi.apply(null,d):ai(d)}};function X(){this.C=this.C;this.v=this.v}X.prototype.C=!1;X.prototype.za=p("C");X.prototype.dispose=function(){this.C||(this.C=!0,this.L())};X.prototype[Symbol.dispose]=function(){this.dispose()};function ci(a,b){b=Ea(ai,b);a.C?b():(a.v||(a.v=[]),a.v.push(b))}X.prototype.L=function(){if(this.v)for(;this.v.length;)this.v.shift()()};var di=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:aa();function ei(a,b){this.l=a;this.o=b;this.j=0;this.g=null}ei.prototype.get=function(){if(this.j>0){this.j--;var a=this.g;this.g=a.next;a.next=null}else a=this.l();return a};function fi(a,b){a.o(b);a.j<100&&(a.j++,b.next=a.g,a.g=b)};var gi=[],hi=[],ji=!1;function ki(a){gi[gi.length]=a;if(ji)for(var b=0;b<hi.length;b++)a(z(hi[b].g,hi[b]))};ki(k());function li(){this.j=this.g=null}li.prototype.add=function(a,b){var c=mi.get();c.set(a,b);this.j?this.j.next=c:this.g=c;this.j=c};li.prototype.remove=function(){var a=null;this.g&&(a=this.g,this.g=this.g.next,this.g||(this.j=null),a.next=null);return a};var mi=new ei(function(){return new ni},function(a){return a.reset()});function ni(){this.next=this.scope=this.g=null}ni.prototype.set=function(a,b){this.g=a;this.scope=b;this.next=null};ni.prototype.reset=function(){this.next=this.scope=this.g=null};var oi,pi=!1,qi=new li;function ri(a,b){oi||si();pi||(oi(),pi=!0);qi.add(a,b)}function si(){var a=Promise.resolve(void 0);oi=function(){a.then(ti)}}function ti(){for(var a;a=qi.remove();){try{a.g.call(a.scope)}catch(b){Ia(b)}fi(mi,a)}pi=!1};function ui(){};function vi(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};function Z(a){this.g=0;this.A=void 0;this.o=this.j=this.l=null;this.v=this.C=!1;if(a!=ui)try{var b=this;a.call(void 0,function(c){wi(b,2,c)},function(c){wi(b,3,c)})}catch(c){wi(this,3,c)}}function xi(){this.next=this.l=this.j=this.o=this.g=null;this.v=!1}xi.prototype.reset=function(){this.l=this.j=this.o=this.g=null;this.v=!1};var yi=new ei(function(){return new xi},function(a){a.reset()});function zi(a,b,c){var d=yi.get();d.o=a;d.j=b;d.l=c;return d}
function Ai(a){if(a instanceof Z)return a;var b=new Z(ui);wi(b,2,a);return b}function Bi(){var a=Error("Requests cancelled because user has been opted out");return new Z(function(b,c){c(a)})}function Ci(a,b,c){Di(a,b,c,null)||ri(Ea(b,a))}function Ei(){var a=[Fi(),Gi()];return new Z(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,n){d--;e[m]=n;d==0&&b(e)},g=function(m){c(m)},h,l=0;l<a.length;l++)h=a[l],Ci(h,Ea(f,l),g);else b(e)})}
function Hi(){var a,b,c=new Z(function(d,e){a=d;b=e});return new Ii(c,a,b)}Z.prototype.then=function(a,b,c){return Ji(this,di(typeof a==="function"?a:null),di(typeof b==="function"?b:null),c)};Z.prototype.$goog_Thenable=!0;r=Z.prototype;r.aa=function(a,b){return Ji(this,null,di(a),b)};r.catch=Z.prototype.aa;r.cancel=function(a){if(this.g==0){var b=new Ki(a);ri(function(){Li(this,b)},this)}};
function Li(a,b){if(a.g==0)if(a.l){var c=a.l;if(c.j){for(var d=0,e=null,f=null,g=c.j;g&&(g.v||(d++,g.g==a&&(e=g),!(e&&d>1)));g=g.next)e||(f=g);e&&(c.g==0&&d==1?Li(c,b):(f?(d=f,d.next==c.o&&(c.o=d),d.next=d.next.next):Mi(c),Ni(c,e,3,b)))}a.l=null}else wi(a,3,b)}function Oi(a,b){a.j||a.g!=2&&a.g!=3||Pi(a);a.o?a.o.next=b:a.j=b;a.o=b}
function Ji(a,b,c,d){var e=zi(null,null,null);e.g=new Z(function(f,g){e.o=b?function(h){try{var l=b.call(d,h);f(l)}catch(m){g(m)}}:f;e.j=c?function(h){try{var l=c.call(d,h);l===void 0&&h instanceof Ki?g(h):f(l)}catch(m){g(m)}}:g});e.g.l=a;Oi(a,e);return e.g}r.zb=function(a){this.g=0;wi(this,2,a)};r.Ab=function(a){this.g=0;wi(this,3,a)};
function wi(a,b,c){a.g==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.g=1,Di(c,a.zb,a.Ab,a)||(a.A=c,a.g=b,a.l=null,Pi(a),b!=3||c instanceof Ki||Qi(a,c)))}function Di(a,b,c,d){if(a instanceof Z)return Oi(a,zi(b||ui,c||null,d)),!0;if(vi(a))return a.then(b,c,d),!0;if(za(a))try{var e=a.then;if(typeof e==="function")return Ri(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1}
function Ri(a,b,c,d,e){function f(l){h||(h=!0,d.call(e,l))}function g(l){h||(h=!0,c.call(e,l))}var h=!1;try{b.call(a,g,f)}catch(l){f(l)}}function Pi(a){a.C||(a.C=!0,ri(a.lb,a))}function Mi(a){var b=null;a.j&&(b=a.j,a.j=b.next,b.next=null);a.j||(a.o=null);return b}r.lb=function(){for(var a;a=Mi(this);)Ni(this,a,this.g,this.A);this.C=!1};
function Ni(a,b,c,d){if(c==3&&b.j&&!b.v)for(;a&&a.v;a=a.l)a.v=!1;if(b.g)b.g.l=null,Si(b,c,d);else try{b.v?b.o.call(b.l):Si(b,c,d)}catch(e){Ti.call(null,e)}fi(yi,b)}function Si(a,b,c){b==2?a.o.call(a.l,c):a.j&&a.j.call(a.l,c)}function Qi(a,b){a.v=!0;ri(function(){a.v&&Ti.call(null,b)})}var Ti=Ia;function Ki(a){Ha.call(this,a);this.g=!1}A(Ki,Ha);Ki.prototype.name="cancel";function Ii(a,b,c){this.promise=a;this.resolve=b;this.reject=c};/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
function Ui(){this.v=[];this.o=this.l=!1;this.j=void 0;this.G=this.K=this.A=!1;this.C=0;this.g=null;this.B=0}Ui.prototype.cancel=function(a){if(this.l)this.j instanceof Ui&&this.j.cancel();else{if(this.g){var b=this.g;delete this.g;a?b.cancel(a):(b.B--,b.B<=0&&b.cancel())}this.G=!0;this.l||(a=new Vi(this),Wi(this),Xi(this,!1,a))}};Ui.prototype.I=function(a,b){this.A=!1;Xi(this,a,b)};function Xi(a,b,c){a.l=!0;a.j=c;a.o=!b;Yi(a)}function Wi(a){if(a.l){if(!a.G)throw new Zi(a);a.G=!1}}
function $i(a){throw a;}function aj(a,b,c){return bj(a,b,null,c)}function cj(a,b,c){bj(a,b,function(d){var e=b.call(this,d);if(e===void 0)throw d;return e},c)}function bj(a,b,c,d){var e=a.l;e||(b===c?b=c=di(b):(b=di(b),c=di(c)));a.v.push([b,c,d]);e&&Yi(a);return a}Ui.prototype.then=function(a,b,c){var d,e,f=new Z(function(g,h){e=g;d=h});bj(this,e,function(g){g instanceof Vi?f.cancel():d(g);return dj},this);return f.then(a,b,c)};Ui.prototype.$goog_Thenable=!0;
Ui.prototype.isError=function(a){return a instanceof Error};function ej(a){return Ya(a.v,function(b){return typeof b[1]==="function"})}var dj={};
function Yi(a){if(a.C&&a.l&&ej(a)){var b=a.C,c=fj[b];c&&(y.clearTimeout(c.g),delete fj[b]);a.C=0}a.g&&(a.g.B--,delete a.g);b=a.j;for(var d=c=!1;a.v.length&&!a.A;){var e=a.v.shift(),f=e[0],g=e[1];e=e[2];if(f=a.o?g:f)try{var h=f.call(e||null,b);h===dj&&(h=void 0);h!==void 0&&(a.o=a.o&&(h==b||a.isError(h)),a.j=b=h);if(vi(b)||typeof y.Promise==="function"&&b instanceof y.Promise)d=!0,a.A=!0}catch(l){b=l,a.o=!0,ej(a)||(c=!0)}}a.j=b;d&&(h=z(a.I,a,!0),d=z(a.I,a,!1),b instanceof Ui?(bj(b,h,d),b.K=!0):b.then(h,
d));c&&(b=new gj(b),fj[b.g]=b,a.C=b.g)}function hj(a){var b=new Ui;Wi(b);Xi(b,!0,a);return b}function Zi(){Ha.call(this)}A(Zi,Ha);Zi.prototype.message="Deferred has already fired";Zi.prototype.name="AlreadyCalledError";function Vi(){Ha.call(this)}A(Vi,Ha);Vi.prototype.message="Deferred was canceled";Vi.prototype.name="CanceledError";function gj(a){this.g=y.setTimeout(z(this.l,this),0);this.j=a}gj.prototype.l=function(){delete fj[this.g];$i(this.j)};var fj={};function ij(){}function jj(a){return a!=null&&!!a.Ea}ij.prototype.Ea=!0;ij.prototype.s=["com.google.apps.docs.xplat.flag.FlagService",1];var kj;function lj(){if(kj==null){var a=new mj(null);kj=function(){return a}}var b;return O((b=kj,b()),jj,ij)};function nj(){}u(nj,K);nj.prototype.get=function(){if(this.j==null){var a=O(y._docs_flag_initialData,ph,oh);this.j=a!=null?a:O({},ph,oh)}return this.j};nj.prototype.g=function(){return this.get()};nj.prototype.s=["com.google.apps.docs.xplat.flag.FlagServiceHelper",0];function oj(a){return typeof a=="string"?a=="true"||a=="1":!!a};function mj(a){this.g=new nj;if(a!=null)for(var b in a){var c=b,d=a[b],e=O(this.g.g(),ph,oh);bf(d)?(d=O(d,bf,$e).g,e[c]=d):e[c]=d!=null?d:null}}u(mj,K);mj.prototype.clear=function(){this.g=new nj};mj.prototype.get=function(a){return O(this.g.g(),ph,oh)[a]};function pj(a,b){a=O(a.g.g(),ph,oh);return b in a}
function qj(a,b){if(!pj(a,b)||a.get(b)==null)return NaN;try{var c=M(a.get(b));Ge==null&&(Ge=RegExp("^\\s*[+-]?(NaN|Infinity|((\\d+\\.?\\d*)|(\\.\\d+))([eE][+-]?\\d+)?[dDfF]?)\\s*$"));if(!Ge.test(c)){var d=new Sg;ae(d,'For input string: "'+M(c)+'"');N(d,Error(d));throw d.g;}return parseFloat(c)}catch(f){var e=fe(f);if(e instanceof Sg)return NaN;throw e.g;}}
function rj(a,b){if(!pj(a,b))return"";a=a.get(b);if(a==null)var c="";else{if(b="number"===typeof a){b=Ie(Q(a));var d=Ie(Q(a));b=b.equals(d)}b?c=""+Ie(Q(a)):c=M(a)}return c}mj.prototype.Ea=!0;mj.prototype.s=["com.google.apps.docs.xplat.flag.FlagServiceImpl",0];function sj(a){hh.call(this,a,null);N(this,Error(this))}u(sj,hh);sj.prototype.s=["com.google.apps.docs.xplat.net.LimitException",0];function tj(a,b,c,d){this.g=!1;this.v=a;this.l=b;this.j=new uj(Math.imul(c,1E3),d)}u(tj,wh);tj.prototype.s=["com.google.apps.docs.xplat.net.QpsLimiter",0];function vj(){this.l=this.o=this.g=0}u(vj,K);function wj(a){return a instanceof vj}vj.prototype.s=["com.google.apps.docs.xplat.util.BasicStat$Slot",0];function uj(a){this.j=0;this.l=a;this.j=Je(a/50);this.g=new xj(af(50))}u(uj,K);uj.prototype.get=function(a){return yj(this,a,function(b,c){b=O(b,bf,$e);c=O(c,wj,vj);return af(b.g+c.g|0)})};function yj(a,b,c){b=b!=null?Q(b):Dd(Kd(Date.now()));zj(a,b);var d=0;b=Aj(a,Q(b));b=Q(b)-a.l;for(var e=a.g.g.length-1|0;e>=0;e=e-1|0){var f=O(a.g.get(e),wj,vj);if(Q(f.j)<=b)break;d=O(c(af(d),f),bf,$e).g}return d}function Aj(a,b){return a.j*Math.floor(b/a.j+1)}
function zj(a,b){var c=O(Bj(a.g),wj,vj);c!=null&&(c=Q(c.j)-a.j,Q(b)<Q(c)&&a.g.clear())}uj.prototype.s=["com.google.apps.docs.xplat.util.BasicStat",0];function xj(a){this.j=this.l=0;a!=null?"number"===typeof a?(a=Q(a),a=Ke(a)):a=a.g:a=100;this.l=a;this.g=O([],nh,mh)}u(xj,K);r=xj.prototype;r.add=function(a){var b=this.g[this.j];this.g[this.j]=a;this.j=Je((this.j+1|0)%this.l);return b};r.get=function(a){a=Cj(this,a);return this.g[a]};r.set=function(a,b){a=Cj(this,a);this.g[a]=b};r.clear=function(){this.j=this.g.length=0};r.ya=function(){for(var a=this.g.length,b=this.g.length-this.g.length|0,c=O([],nh,mh);b<a;b=b+1|0){var d=c,e=this.get(b);d.push(e)}return c};
function Bj(a){return a.g.length==0?null:a.get(a.g.length-1|0)}function Cj(a,b){if(b>=a.g.length)throw le().g;return a.g.length<a.l?b:Je((a.j+b|0)%a.l)}r.s=["com.google.apps.docs.xplat.util.CircularBuffer",0];var Dj,Ej,Fj,Gj,Hj,Ij,Jj,Kj,Lj,Mj,Nj,Oj,Pj,Qj,Rj,Sj,Tj;
function Uj(){Uj=k();Dj=T();Ej=T();Fj=mf(Wg("Trusted Type;TrustedHTML;TrustedScript;cannot communicate with background;zaloJSV2;kaspersky-labs;@user-script;Object Not Found Matching Id;contextChanged;Not implemented on this platform;Extension context invalidated;neurosurgeonundergo;realTimeClData;Failed to execute 'querySelectorAll' on 'Document';Promise.all(...).then(...).catch(...).finally is not a function;Error executing Chrome API, chrome.tabs;zotero;Identifier 'originalPrompt' has already been declared;User rejected the request;Could not inject ethereum provider because it's not your default extension;Cannot redefine property: googletag;Can't find variable: HTMLDialogElement;Identifier 'listenerName' has already been declared;Cannot read properties of undefined (reading 'info');Permission denied to access property \"type\";Error: Promise timed out;Request timeout ToolbarStatus;Can't find variable: nc;imtgo;ton is not a function".split(";")));Gj=
mf(Wg("puppeteer-core;kaspersky-labs;@user-script;jsQuilting;linkbolic;neurosurgeonundergo;tlscdn;https://cdnjs.cloudflare.com/ajax/libs/mathjax/;secured-pixel.com;Can't find variable: nc;imtgo;_simulateEvent".split(";")));Hj=mf(Wg("egfdjlfmgnehecnclamagfafdccgfndp mndnfokpggljbaajbnioimlmbfngpief mlkejohendkgipaomdopolhpbihbhfnf kgonammgkackdilhodbgbmodpepjocdp klbcgckkldhdhonijdbnhhaiedfkllef pmehocpgjmkenlokgjfkaichfjdhpeol cjlaeehoipngghikfjogbdkpbdgebppb ghbmnnjooekpmoecnnnilnnbdlolhkhi lmjegmlicamnimmfhcmpkclmigmmcbeh gmbmikajjgmnabiglmofipeabaddhgne lpcaedmchfhocbbapmcbpinfpgnhiddi gbkeegbaiigmenfmjfclcdgdpimamgkj adokjfanaflbkibffcbhihgihpgijcei".split(" ")));
Ij=T(W("chrome-extension://([^\\/]+)"),W("moz-extension://([^\\/]+)"),W("ms-browser-extension://([^\\/]+)"),W("webkit-masked-url://([^\\/]+)"),W("safari-web-extension://([^\\/]+)"));Jj=T("There was an error during the transport or processing of this request","Failed to retrieve dependencies of service","Failed to load gapi","Rpc failed due to xhr error. error code: 6, error:  [0]","An interceptor has requested that the request be retried",'8,"generic"',"A network error occurred");Kj=T("status is 0, navigator.onLine =",
"Network sync is disabled. Aborting a network request of int type","The service is currently unavailable.","Internal error encountered.","A network error occurred and the request could not be completed.","data does not exist in AF cache");Lj=T(W("^Permission denied$"));Mj=T("Kg is not defined","uncaught error","The play method is not allowed by the user agent or the platform in the current context, possibly because the user denied permission.","Illegal invocation","Script error","zCommon","can't access dead object",
"Java exception was raised during method invocation","pauseVideo is not a function","ResizeObserver loop");Nj=T(W("phantomjs|node:electron|py-scrap|eval code|Program Files"));Oj=T("Cannot read properties of null (reading 'requestAnimationFrame')","Class extends value undefined is not a constructor or null","GM3TooltipService: No tooltip with id","Mole was disposed","getInitialTopicListResponse is missing for stream rendering","getPeopleById call preempted","The operation is insecure","class heritage",
"The play() request was interrupted");Pj=T(W("Script https:\\/\\/meet.google.com\\/.*meetsw.*load failed"),W("A bad HTTP response code \\(\\d+\\) was received when fetching the script"));Qj=mf(Wg("Service worker registration is disabled by MDA;An unknown error occurred when fetching the script;Operation has been aborted;Timed out while trying to start the Service Worker;The Service Worker system has shutdown;The user denied permission to use Service Worker;The script resource is behind a redirect, which is disallowed;The document is in an invalid state;ServiceWorker script evaluation failed;ServiceWorker cannot be started;Failed to access storage;Worker disallowed;encountered an error during installation".split(";")));
Rj=T(W("Error loading.*Consecutive load failures"),W("Failed to load module.*Consecutive load failures"));Sj=T(W("Error loading.*Consecutive load failures"),W("Failed to load module.*Consecutive load failures"));Tj=T("Timeout reached for loading script https://www.gstatic.com/_/apps-fileview/_/js/","Error while loading script https://www.gstatic.com/_/apps-fileview/_/js/")};function Vj(){}u(Vj,K);function Wj(a){return a instanceof Vj}Vj.prototype.s=["com.google.apps.telemetry.xplat.error.ErrorClassifier",0];function kf(){}u(kf,Vj);kf.prototype.C=function(a){a=M(a.getMessage())+"\n"+M(a.g)+"\n"+M(Xj(a));a:{for(var b=!1,c=(Uj(),Ij).F();c.g();){var d=O(c.j(),rh,qh);for(d=th(d,a);uh(d);){b=d;if(b.g==null)throw a=new xe,ae(a,"No match available"),N(a,Error(a)),a.g;if(1>(b.g.length-1|0))throw me("No group 1").g;b=O(b.g[1],ig,Ue);Pg();b=b==null?Mg:Ng(Tg(b));b=O(b.g!=null?b.g:"",ig,Ue);if(Hj.contains(b)){a=!1;break a}b=!0}}a=b}return a};
kf.prototype.s=["com.google.apps.telemetry.xplat.error.BaseExtensionErrorClassifier",0];function Yj(){}u(Yj,K);Yj.prototype.equals=function(a){return Zj(this,a)};Yj.prototype.R=function(){for(var a=1,b=ak(this),c=0;c<b.length;c++){var d=this[b[c]];if(d!=null){if(d.T)if(d==null)d=0;else{for(var e=1,f=0;f<d.length;f++)e=Math.imul(31,e)+Ye(d[f])|0;d=e}else d=Te(d);a=Math.imul(1000003,a)^d}}return a};
Yj.prototype.toString=function(){var a=Le(this);a=gh(gh(fh(a),"."),"$");a=Xg(a,a.lastIndexOf("AutoValue_")+1|0);a=ff(M(a)+"{","}");for(var b=ak(this),c=0;c<b.length;c++){var d=b[c],e=this[d];Array.isArray(e)&&(e="["+M(e)+"]");gf(a,M(d)+"="+M(e))}return a.toString()};
function Zj(a,b){if(b==null||!L(Le(b),Le(a)))return!1;var c=ak(a);if(c.length!=ak(b).length)return!1;for(var d=0;d<c.length;d++){var e=c[d],f=a[e];e=b[e];if(!(L(f,e)||(f==null||e==null?0:f.T&&e.T?L(Le(f),Le(e))&&Rg(f,e):Se(f,e))))return!1}return!0}Yj.prototype.s=["javaemul.internal.ValueType",0];function ak(a){var b=Object.keys(a),c=a.C;return c?b.filter(function(d){return!c.includes(d)}):b};function bk(){}u(bk,Yj);bk.prototype.s=["com.google.apps.telemetry.xplat.error.ErrorClassification",0];function ck(){}u(ck,K);ck.prototype.s=["com.google.apps.telemetry.xplat.error.JsError$Builder",0];function dk(){}u(dk,Yj);function Xj(a){var b="";a.j!=null&&(b=M(b)+(M(a.j)+"\n"));a.l!=null&&(b=M(b)+M(a.l));return b}dk.prototype.getMessage=p("o");dk.prototype.s=["com.google.apps.telemetry.xplat.error.JsError",0];function ek(){this.g=!1}var fk,gk,hk,ik,jk,kk,lk,mk,nk,ok,pk,lf;u(ek,K);
function qk(a,b){var c=b.v,d=Pf();try{a.g&&d.X("apps_telemetry.after_downgraded_severe","true");for(var e=0;e<a.j.size();e=e+1|0){var f=O(a.j.W(e),Wj,Vj);if(f.C(b)){var g=f.l,h=f.j,l=new bk;Ze(g);l.j=g;Ze(h);l.g=h;var m=l}else m=null;var n=m;if(n!=null){b=c;e=c;f=pk;var w=f.contains;m=e;var x=(ah(),$g);if(w.call(f,L(x,Zg)?m.toLocaleUpperCase():m.toUpperCase())){a.g=!0;var q="WARNING"}else q=e;w=b;x=q;var H=Pf();H.X("apps_telemetry.classification",""+n.j);H.X("apps_telemetry.classification_code",n.g!=
null?""+n.g:"");H.X("apps_telemetry.incoming_severity",w);H.X("apps_telemetry.outgoing_severity",x);n=d;Q(H);for(var Y=H.Y().F();Y.g();){var la=O(Y.j(),U,V);n.X(la.N(),la.P())}c=q;break}}d.X("apps_telemetry.processed","true")}catch(mc){var pb=fe(mc);if(pb instanceof P)d.X("apps_telemetry.processed","false");else throw pb.g;}q=new rk;Ze(c);q.j=c;Ze(d);q.g=d;return q}
function sk(){sk=k();lf=tk((Uj(),Fj),Gj,1);gk=tk(Jj,Dj,2);ik=tk(Mj,Dj,3);hk=uk(Lj,Nj,3);kk=tk(Oj,Dj,3);jk=tk(Kj,Dj,2);nk=uk(Rj,Sj,4);ok=tk(Tj,Dj,4);lk=uk(Pj,Ej,5);mk=tk(Qj,Dj,5);fk=jf();pk=Lf("SEVERE","SEVERE_AFTER_INITIAL","FATAL","UNKNOWN","")}ek.prototype.s=["com.google.apps.telemetry.xplat.error.ErrorProcessor",0];function rk(){}u(rk,Yj);rk.prototype.s=["com.google.apps.telemetry.xplat.error.ErrorProcessorResult",0];function vk(){}u(vk,Vj);function uk(a,b,c){var d=new vk;d.l=c;d.j=0;d.g=a;d.o=b;return d}vk.prototype.C=function(a){var b=Xj(a);return wk(a.getMessage(),this.g)||wk(a.g,this.o)||wk(b,this.g)||wk(b,this.o)};function wk(a,b){for(b=b.F();b.g();){var c=O(b.j(),rh,qh);if(uh(th(c,a)))return!0}return!1}vk.prototype.s=["com.google.apps.telemetry.xplat.error.RegexErrorClassifier",0];function xk(){this.o=!1}u(xk,Vj);xk.prototype.C=function(a){if(this.o)a:{a=a.getMessage();for(var b=0;b<this.g.size();b=b+1|0){var c=a,d=O(this.g.W(b),ig,Ue);if(L(Q(c),d)){a=!0;break a}}a=!1}else a=yk(a.getMessage(),this.g)||yk(a.g,this.v)||yk(Xj(a),this.g)||yk(Xj(a),this.v);return a};function yk(a,b){for(var c=0;c<b.size();c=c+1|0){var d=a,e=O(b.W(c),ig,Ue);if(d.indexOf(e.toString())!=-1)return!0}return!1}function tk(a,b,c){var d=new xk;d.l=c;d.j=0;d.g=a;d.v=b;d.o=!1;return d}
xk.prototype.s=["com.google.apps.telemetry.xplat.error.StringErrorClassifier",0];function zk(a,b,c){for(var d in a)b.call(c,a[d],d,a)}function Ak(a){var b={},c;for(c in a)b[c]=a[c];return b}var Bk="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Ck(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Bk.length;f++)c=Bk[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function Dk(a){this.g=this.A=this.o="";this.B=null;this.C=this.j="";this.v=!1;var b;a instanceof Dk?(this.v=a.v,Ek(this,a.o),this.A=a.A,this.g=a.g,Fk(this,a.B),Gk(this,a.j),Hk(this,Ik(a.l)),this.C=a.C):a&&(b=String(a).match(Uh))?(this.v=!1,Ek(this,b[1]||"",!0),this.A=Jk(b[2]||""),this.g=Jk(b[3]||"",!0),Fk(this,b[4]),Gk(this,b[5]||"",!0),Hk(this,b[6]||"",!0),this.C=Jk(b[7]||"")):(this.v=!1,this.l=new Kk(null,this.v))}
Dk.prototype.toString=function(){var a=[],b=this.o;b&&a.push(Lk(b,Mk,!0),":");var c=this.g;if(c||b=="file")a.push("//"),(b=this.A)&&a.push(Lk(b,Mk,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.B,c!=null&&a.push(":",String(c));if(c=this.j)this.g&&c.charAt(0)!="/"&&a.push("/"),a.push(Lk(c,c.charAt(0)=="/"?Nk:Ok,!0));(c=this.l.toString())&&a.push("?",c);(c=this.C)&&a.push("#",Lk(c,Pk));return a.join("")};
Dk.prototype.resolve=function(a){var b=new Dk(this),c=!!a.o;c?Ek(b,a.o):c=!!a.A;c?b.A=a.A:c=!!a.g;c?b.g=a.g:c=a.B!=null;var d=a.j;if(c)Fk(b,a.B);else if(c=!!a.j){if(d.charAt(0)!="/")if(this.g&&!this.j)d="/"+d;else{var e=b.j.lastIndexOf("/");e!=-1&&(d=b.j.slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(e.indexOf("./")!=-1||e.indexOf("/.")!=-1){d=e.lastIndexOf("/",0)==0;e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];h=="."?d&&g==e.length&&f.push(""):h==".."?((f.length>1||f.length==1&&
f[0]!="")&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?Gk(b,d):c=a.l.toString()!=="";c?Hk(b,Ik(a.l)):c=!!a.C;c&&(b.C=a.C);return b};function Ek(a,b,c){a.o=c?Jk(b,!0):b;a.o&&(a.o=a.o.replace(/:$/,""));return a}function Fk(a,b){if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("Bad port number "+b);a.B=b}else a.B=null}function Gk(a,b,c){a.j=c?Jk(b,!0):b;return a}function Hk(a,b,c){b instanceof Kk?(a.l=b,Qk(a.l,a.v)):(c||(b=Lk(b,Rk)),a.l=new Kk(b,a.v))}
function Sk(a,b,c){a.l.set(b,c);return a}function Jk(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function Lk(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,Tk),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function Tk(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}var Mk=/[#\/\?@]/g,Ok=/[#\?:]/g,Nk=/[#\?]/g,Rk=/[#\?@]/g,Pk=/#/g;function Kk(a,b){this.j=this.g=null;this.l=a||null;this.o=!!b}
function Uk(a){a.g||(a.g=new Map,a.j=0,a.l&&Vh(a.l,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}r=Kk.prototype;r.add=function(a,b){Uk(this);this.l=null;a=Vk(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.j=this.j+1;return this};r.remove=function(a){Uk(this);a=Vk(this,a);return this.g.has(a)?(this.l=null,this.j=this.j-this.g.get(a).length,this.g.delete(a)):!1};r.clear=function(){this.g=this.l=null;this.j=0};
function Wk(a,b){Uk(a);b=Vk(a,b);return a.g.has(b)}r.forEach=function(a,b){Uk(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};r.ya=function(a){Uk(this);var b=[];if(typeof a==="string")Wk(this,a)&&(b=b.concat(this.g.get(Vk(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
r.set=function(a,b){Uk(this);this.l=null;a=Vk(this,a);Wk(this,a)&&(this.j=this.j-this.g.get(a).length);this.g.set(a,[b]);this.j=this.j+1;return this};r.get=function(a,b){if(!a)return b;a=this.ya(a);return a.length>0?String(a[0]):b};
r.toString=function(){if(this.l)return this.l;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.ya(d);for(var f=0;f<d.length;f++){var g=e;d[f]!==""&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.l=a.join("&")};function Ik(a){var b=new Kk;b.l=a.l;a.g&&(b.g=new Map(a.g),b.j=a.j);return b}function Vk(a,b){b=String(b);a.o&&(b=b.toLowerCase());return b}
function Qk(a,b){b&&!a.o&&(Uk(a),a.l=null,a.g.forEach(function(c,d){var e=d.toLowerCase();if(d!=e&&(this.remove(d),this.remove(e),c.length>0)){this.l=null;d=this.g;var f=d.set;e=Vk(this,e);var g=c.length;if(g>0){for(var h=Array(g),l=0;l<g;l++)h[l]=c[l];g=h}else g=[];f.call(d,e,g);this.j=this.j+c.length}},a));a.o=b};function Xk(){var a=y.window;a.onbeforeunload=k();a.location.reload()};function Yk(){this.g=function(){Xk()}}Yk.prototype.notify=function(){window.confirm("This error has been reported to Google and we'll look into it as soon as possible. Please reload this page to continue.")&&this.g()};function Zk(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=this.ja=!1}Zk.prototype.stopPropagation=function(){this.ja=!0};Zk.prototype.preventDefault=function(){this.defaultPrevented=!0};var $k=function(){if(!y.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=k();y.addEventListener("test",c,b);y.removeEventListener("test",c,b)}catch(d){}return a}();function al(a,b){Zk.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.g=null;a&&this.init(a,b)}A(al,Zk);
al.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=a.offsetX,this.offsetY=a.offsetY,this.clientX=
a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.g=a;a.defaultPrevented&&al.Z.preventDefault.call(this)};
al.prototype.stopPropagation=function(){al.Z.stopPropagation.call(this);this.g.stopPropagation?this.g.stopPropagation():this.g.cancelBubble=!0};al.prototype.preventDefault=function(){al.Z.preventDefault.call(this);var a=this.g;a.preventDefault?a.preventDefault():a.returnValue=!1};var bl="closure_listenable_"+(Math.random()*1E6|0);var cl=0;function dl(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.sa=e;this.key=++cl;this.removed=this.pa=!1}function el(a){a.removed=!0;a.listener=null;a.proxy=null;a.src=null;a.sa=null};function fl(a){this.src=a;this.g={};this.j=0}fl.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.j++);var g=gl(a,b,d,e);g>-1?(b=a[g],c||(b.pa=!1)):(b=new dl(b,this.src,f,!!d,e),b.pa=c,a.push(b));return b};fl.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.g))return!1;var e=this.g[a];b=gl(e,b,c,d);return b>-1?(el(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.g[a],this.j--),!0):!1};
function hl(a,b){var c=b.type;c in a.g&&Za(a.g[c],b)&&(el(b),a.g[c].length==0&&(delete a.g[c],a.j--))}fl.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.g)if(!a||c==a){for(var d=this.g[c],e=0;e<d.length;e++)++b,el(d[e]);delete this.g[c];this.j--}return b};function gl(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.removed&&f.listener==b&&f.capture==!!c&&f.sa==d)return e}return-1};var il="closure_lm_"+(Math.random()*1E6|0),jl={},kl=0;function ll(a,b,c,d,e){if(d&&d.once)return ml(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)ll(a,b[f],c,d,e);return null}c=nl(c);return a&&a[bl]?a.listen(b,c,za(d)?!!d.capture:!!d,e):ol(a,b,c,!1,d,e)}
function ol(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=za(e)?!!e.capture:!!e,h=pl(a);h||(a[il]=h=new fl(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=ql();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)$k||(e=g),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(rl(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");kl++;return c}
function ql(){function a(c){return b.call(a.src,a.listener,c)}var b=sl;return a}function ml(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)ml(a,b[f],c,d,e);return null}c=nl(c);return a&&a[bl]?a.j.add(String(b),c,!0,za(d)?!!d.capture:!!d,e):ol(a,b,c,!0,d,e)}
function tl(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)tl(a,b[f],c,d,e);else(d=za(d)?!!d.capture:!!d,c=nl(c),a&&a[bl])?a.j.remove(String(b),c,d,e):a&&(a=pl(a))&&(b=a.g[b.toString()],a=-1,b&&(a=gl(b,c,d,e)),(c=a>-1?b[a]:null)&&ul(c))}
function ul(a){if(typeof a!=="number"&&a&&!a.removed){var b=a.src;if(b&&b[bl])hl(b.j,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(rl(c),d):b.addListener&&b.removeListener&&b.removeListener(d);kl--;(c=pl(b))?(hl(c,a),c.j==0&&(c.src=null,b[il]=null)):el(a)}}}function rl(a){return a in jl?jl[a]:jl[a]="on"+a}
function sl(a,b){if(a.removed)a=!0;else{b=new al(b,this);var c=a.listener,d=a.sa||a.src;a.pa&&ul(a);a=c.call(d,b)}return a}function pl(a){a=a[il];return a instanceof fl?a:null}var vl="__closure_events_fn_"+(Math.random()*1E9>>>0);function nl(a){if(typeof a==="function")return a;a[vl]||(a[vl]=function(b){return a.handleEvent(b)});return a[vl]}ki(function(a){sl=a(sl)});function wl(a,b){Zk.call(this,a);this.error=b}u(wl,Zk);var xl=/\/d\/([^\/]+)/,yl=/\/r\/([^\/]+)/;function zl(a){a=a.match(Uh)[5]||null;return xl.test(a)}function Al(a,b){if(zl(a)){zl(a);var c=a.match(Uh),d=c[5];d=d.replace(b,"");b=c[1];a=c[2];var e=c[3],f=c[4],g=c[6];c=c[7];var h="";b&&(h+=b+":");e&&(h+="//",a&&(h+=a+"@"),h+=e,f&&(h+=":"+f));d&&(h+=d);g&&(h+="?"+g);c&&(h+="#"+c);b=h}else b=a;return b};function Bl(){X.call(this);this.j=new fl(this);this.Ua=this;this.U=null}A(Bl,X);Bl.prototype[bl]=!0;r=Bl.prototype;r.addEventListener=function(a,b,c,d){ll(this,a,b,c,d)};r.removeEventListener=function(a,b,c,d){tl(this,a,b,c,d)};
r.dispatchEvent=function(a){var b=this.U;if(b){var c=[];for(var d=1;b;b=b.U)c.push(b),++d}b=this.Ua;d=a.type||a;if(typeof a==="string")a=new Zk(a,b);else if(a instanceof Zk)a.target=a.target||b;else{var e=a;a=new Zk(d,b);Ck(a,e)}e=!0;var f;if(c)for(f=c.length-1;!a.ja&&f>=0;f--){var g=a.currentTarget=c[f];e=Cl(g,d,!0,a)&&e}a.ja||(g=a.currentTarget=b,e=Cl(g,d,!0,a)&&e,a.ja||(e=Cl(g,d,!1,a)&&e));if(c)for(f=0;!a.ja&&f<c.length;f++)g=a.currentTarget=c[f],e=Cl(g,d,!1,a)&&e;return e};
r.L=function(){Bl.Z.L.call(this);this.j&&this.j.removeAll(void 0);this.U=null};r.listen=function(a,b,c,d){return this.j.add(String(a),b,!1,c,d)};function Cl(a,b,c,d){b=a.j.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.removed&&g.capture==c){var h=g.listener,l=g.sa||g.src;g.pa&&hl(a.j,g);e=h.call(l,d)!==!1&&e}}return e&&!d.defaultPrevented};function Dl(a,b,c){if(typeof a==="function")c&&(a=z(a,c));else if(a&&typeof a.handleEvent=="function")a=z(a.handleEvent,a);else throw Error("Invalid listener argument");return Number(b)>2147483647?-1:y.setTimeout(a,b||0)}function El(){var a=null;return(new Z(function(b,c){a=Dl(function(){b(void 0)},2E3);a==-1&&c(Error("Failed to schedule timer."))})).aa(function(b){y.clearTimeout(a);throw b;})};function Fl(a,b,c){X.call(this);this.g=a;this.l=b||0;this.j=c;this.o=z(this.kb,this)}A(Fl,X);r=Fl.prototype;r.ha=0;r.L=function(){Fl.Z.L.call(this);this.stop();delete this.g;delete this.j};r.start=function(a){this.stop();this.ha=Dl(this.o,a!==void 0?a:this.l)};r.stop=function(){this.isActive()&&y.clearTimeout(this.ha);this.ha=0};r.isActive=function(){return this.ha!=0};r.kb=function(){this.ha=0;this.g&&this.g.call(this.j)};function Gl(a,b,c,d){X.call(this);this.l=d!=null?d:.15;this.o=a;this.B=b;this.G=c;this.g=new Fl(this.xb,void 0,this);this.A=Number.NEGATIVE_INFINITY;this.j=0}u(Gl,X);r=Gl.prototype;r.isActive=function(){return this.g.isActive()};r.start=function(){Hl(this,!1)};function Hl(a,b){b&&(a.g.stop(),Il(a,a.B));a.isActive()||(b=Math.max(0,a.A+a.j-Date.now()),b==0&&(a.j=0),a.g.start(b))}r.stop=function(){this.g.stop()};function Il(a,b){b>0&&a.l!=0&&(b=Math.floor(b*(1-a.l+Math.random()*a.l*2)));a.j=b}
r.xb=function(){this.A=Date.now();Il(this,Math.min(Math.max(this.j*2,this.B),this.G));this.o()};r.L=function(){this.g.dispose();delete this.g;delete this.o;X.prototype.L.call(this)};function Jl(a){X.call(this);this.j=a;this.g={}}A(Jl,X);var Kl=[];Jl.prototype.listen=function(a,b,c,d){Array.isArray(b)||(b&&(Kl[0]=b.toString()),b=Kl);for(var e=0;e<b.length;e++){var f=ll(a,b[e],c||this.handleEvent,d||!1,this.j||this);if(!f)break;this.g[f.key]=f}return this};Jl.prototype.removeAll=function(){zk(this.g,function(a,b){this.g.hasOwnProperty(b)&&ul(a)},this);this.g={}};Jl.prototype.L=function(){Jl.Z.L.call(this);this.removeAll()};
Jl.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};function Ll(a,b,c,d,e){X.call(this);var f=this;this.g=a;this.S=b;this.l=oj(this.S.get("docs-eir"));this.B=new Fl(this.G,3E4,this);this.j=new Gl(function(){return f.G()},3E4,36E5);this.A=0;this.I=null;this.V=new tj("errorsender",1,8,d);ci(this,this.V);this.O=!1;this.M=null;this.U=new Set;this.K=new Jl(this);this.na=c||10;this.ca=e||null;this.K.listen(this.g,"complete",this.ma);this.K.listen(this.g,"ready",this.G)}u(Ll,X);
Ll.prototype.send=function(a,b,c,d){oj(this.S.get("docs-dafjera"))&&(a=Al(Al(a,yl),xl));var e=aj(aj(hj(this.o.length),function(f){if(!(f>=this.na))return f={},f.u=a,f.m=b,f.c=c,f.h=d,this.enqueue(f)},this),this.G,this);cj(e,function(){this.U.delete(e)},this);this.U.add(e)};Ll.prototype.G=function(){return(this.l?this.j.isActive():this.B.isActive())||this.g.isActive()||this.O?hj():Ml(this)};
function Ml(a){a.l?a.j.isActive():a.B.isActive();a.g.isActive();return function(){return aj(hj(a.o[0]!==void 0?a.o[0]:null),function(b){return Nl(a,b)})}()}
function Nl(a,b){if((a.l?a.j.isActive():a.B.isActive())||a.g.isActive()||a.O)return hj();if(!b)return a.l&&a.j.stop(),hj();if(b.u.length>4E3)return Ol(a);try{var c=a.V;if(!((c.j.get(null)+1|0)/Q(c.j.l/1E3)<=c.l))throw(new sj("Query would cause "+M(c.v)+" to exceed "+c.l+" qps.")).g;var d=c.j,e=Dd(Kd(Date.now()));zj(d,e);var f=O(Bj(d.g),wj,vj);if(f==null||Q(e)>=Q(f.j)){var g=Aj(d,Q(e)),h=new vj;h.j=g;h.g=0;h.o=2147483647;h.l=-2147483648;f=h;d.g.add(f)}f.g=f.g+1|0;f.o=Math.min(1,f.o);f.l=Math.max(1,
f.l);a.M=new Ui;var l=b.u;a.ca!=null&&(l=$h(l,"reportingSessionId",a.ca));a.A>0&&(l=$h(l,"retryCount",a.A));a.I!=null&&(l=$h(l,"previousErrorSendStatus",a.I));a.g.send(l,b.m,b.c,b.h);return a.M}catch(m){b=m;if(b==null)b=new $d,be(b),N(b,Error(b));else if(ie(b))b=O(b,ie,$d);else if(jh(b))b=O(b,jh,ih),b=fe(b);else throw we("Unsupported type cannot be used to create a Throwable.").g;if(b instanceof sj)a.O=!0;else throw Jh(m,{"docs-origin-class":"docs.debug.ErrorSender"});}return hj()}
Ll.prototype.ma=function(){var a=Pl(this.g),b=this.M,c=Ql(this.g)||a>=400&&a<=500,d=this.l?this.A>30:!1;c||d?(this.A=0,this.I=null,this.l&&this.j.stop(),aj(Ol(this),function(){Wi(b);Xi(b,!0)})):(this.A++,this.I=a===-1?this.g.B:a,this.l?this.A!=1||this.j.isActive()?this.j.start():Hl(this.j,!0):this.B.start(),Wi(b),Xi(b,!0))};Ll.prototype.L=function(){bi(this.K,this.B,this.j,this.g);this.U.clear();X.prototype.L.call(this)};function Rl(a,b,c,d){Ll.call(this,a,b,c,void 0,d);this.o=[]}u(Rl,Ll);Rl.prototype.enqueue=function(a){this.o.push(a);return hj()};function Ol(a){a.o.shift();return hj()}Rl.prototype.L=function(){delete this.o;Ll.prototype.L.call(this)};function Sl(a){this.g=nd(Ud(),Fc(a));a=fd(this.g,1);this.j=Math.floor(Math.random()*100)<a}Sl.prototype.toString=function(){var a="{bool="+!(this.j?!ed(this.g,5):!ed(this.g,2))+', string="',b=this.j?jd(this.g,6):hd(this.g,3);a=a+(b!=null?String(b):"")+'", int=';b=this.j?pc(F(this.g,7,void 0,Pc)):fd(this.g,4,-1);return a+(b!=null?Number(b):-1)+"}"};function Tl(a){this.g=new Map;this.j=[];if(a=a.get("docs-cei")){var b=a.i;b&&$a(this.j,b);a=a.cf||{};for(var c in a)this.g.set(c,new Sl(a[c]))}}Tl.prototype.get=function(a){return this.g.get(a)||null};function Ul(){for(var a in Array.prototype)return!1;return!0};function Vl(a){this.g=a}function Wl(a){var b=a.g;if(b==null)return null;if(typeof b==="string")return b;throw new TypeError("Invalid string data <K1cgmc>: "+a.g+" (typeof "+typeof a.g+")");}Vl.prototype.toString=function(){var a=Wl(this);if(a===null)throw Error("Data K1cgmc not defined.");return a};function Xl(a){this.D=E(a)}u(Xl,I);function Yl(a){this.D=E(a)}u(Yl,I);var Zl=[4,5];function $l(a){this.D=E(a)}u($l,I);function am(){var a=y;a=a===void 0?window:a;var b=new Vl(Md("K1cgmc",a));a=new $l;b=Wl(b);b!==null&&(b.indexOf("%.@."),a=md($l,"["+b.substring(4)));b=a.D;var c=b[C]|0;this.g=Gb(a,c)?a:Jc(a,b,c)?Kc(a,b):new a.constructor(Ic(b,c,!0))}
am.prototype.Ma=function(){var a=new Map,b;(b=this.g)==null?b=void 0:(b=bd(b,Yl,1),b=bd(b,Xl,Zc(b,Zl,4)));if(b==null?0:oc(F(b,2))!=null){var c,d=(c=id(b,2))==null?void 0:c.toString();d&&a.set("canaryanalysisservertestgroup",d);if(b==null)var e=void 0;else if((c=G(b,Qd,3))==null)e=void 0;else{b=Number;e=e===void 0?"0":e;d=F(c,1);var f=!0;f=f===void 0?!1:f;var g=typeof d;d!=null&&(g==="bigint"?d=String(dc(64,d)):nc(d)?g==="string"?(nc(d),f=hc(Number(d)),ec(f)?d=String(f):(f=d.indexOf("."),f!==-1&&(d=
d.substring(0,f)),d=rc(d))):d=f?tc(d):sc(d):d=void 0);e=b(d!=null?d:e);c=fd(c,2);e=(new Date(e*1E3+c/1E6)).valueOf().toString()}e&&a.set("serverstarttimemillis",e)}var h,l;(e=(h=this.g)==null?void 0:(l=G(h,Yl,1))==null?void 0:id(l,6))&&a.set("clientApp",String(e));return a};function bm(){}bm.prototype.Ma=function(){var a=new Map;cm()&&a.set("apps_telemetry.screen_tampered","true");a:{var b=v(Array.prototype);for(b=b.next();!b.done;b=b.next()){b=!0;break a}b=!1}b&&a.set("apps_telemetry.array_prototype_tampered","true");return a};function cm(){var a=y.screen,b=!(a instanceof Screen);try{var c=k();a.addEventListener("change",c);a.removeEventListener("change",c)}catch(d){b=!0}return b};function dm(a){if(a instanceof Error||a&&a.message!==void 0)return a.message;var b="";try{b=a&&a instanceof Object?JSON.stringify(a):String(a)}catch(c){b=String(a)}return b}function em(a){return a instanceof Error||a&&a.stack!==void 0?a.stack||"":""}
function fm(a,b){var c=a instanceof Error||a&&a.cause!==void 0?a.cause:null,d=new ck;Ze("");d.g="";var e=dm(a);Ze(e);d.j=e;a=em(a);Ze(a);d.l=a;b&&(Ze(b),d.g=b);c&&(d.o=dm(c),d.v=em(c));if(d.j==null||d.l==null||d.g==null)throw ye().g;b=d.l;c=d.o;a=d.v;e=d.g;var f=new dk;f.o=d.j;f.g=b;f.j=c;f.l=a;f.v=e;return f};/*

Math.uuid.js (v1.4)
http://www.broofa.com
mailto:<EMAIL>
Copyright (c) 2010 Robert Kieffer
Dual licensed under the MIT and GPL licenses.
*/
var gm="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");function hm(){if(b===void 0)try{var a=Pd(Sd)}catch(H){a=!1}else a=b;var b=a;var c=c===void 0?[]:c;try{var d=Pd(Rd),e=void 0===Ib?2:4;a=void 0;var f=d.D,g=f[C]|0,h=Gb(d,g)?1:e;a=!!a||h===3;h===2&&Mc(d)&&(f=d.D,g=f[C]|0);var l=Wc(f,1),m=l===zb?7:l[C]|0,n=Xc(m,g);if(d=4&n?!1:!0){4&n&&(l=Array.prototype.slice.call(l),m=0,n=Vc(n,g),g=Sc(f,g,1,l));for(var w=e=0;e<l.length;e++){var x=uc(l[e]);x!=null&&(l[w++]=x)}w<e&&(l.length=w);x=n|=4;x&=-513;n=x&-1025;n&=-4097}n!==m&&(Bb(l,n),2&n&&Object.freeze(l));var q=
l=Tc(l,n,f,g,1,h,d,a)}catch(H){q=[]}f=b;g=q;b=[Error("uncaught error").message];sk();q=zf();if(g.length!=0){h=q.add;l=zf();for(m=0;m<g.length;m=m+1|0)l.add(W(O(g[m],ig,Ue)));h.call(q,uk(l,l,7))}q.ua(fk);q.add(gk);q.add(hk);q.add(ik);f&&(q.add(jk),q.add(kk),q.add(lk),q.add(mk),q.add(nk),q.add(ok));for(f=0;f<c.length;f=f+1|0)q.add(O(c[f],Wj,Vj));if(b.length!=0){c=zf();for(f=0;f<b.length;f=f+1|0)c.add(O(b[f],ig,Ue));b=q.add;f=new xk;g=zf();f.l=3;f.j=5;f.g=c;f.v=g;f.o=!0;b.call(q,f)}c=new ek;c.g=!1;c.j=
q;this.l=c;this.j=[new am,new bm];c=[];c[8]=c[13]=c[18]=c[23]="-";c[14]="4";for(q=0;q<36;q++)c[q]||(b=0|Math.random()*16,c[q]=gm[q==19?b&3|8:b]);this.g=c.join("")}function im(a,b,c,d){try{d["apps_telemetry.session_id"]=a.g;"apps_telemetry.processed"in d&&(d["apps_telemetry.multi_processed"]="true");var e=fm(b,c),f=qk(a.l,e),g=f.g,h=jm(a);g.Oa().Ra().forEach(function(l){h.set(l,g.get(l))});h.forEach(function(l,m){d[m]=l});return f.j}catch(l){d["apps_telemetry.processed"]="false"}return c}
function jm(a){var b=new Map;a=v(a.j);for(var c=a.next();!c.done;c=a.next())c.value.Ma().forEach(function(d,e){b.set(e,d)});return b};y.U3bHHf!=null||(y.U3bHHf=0);y.U3bHHf++;function km(a,b){var c=a.__wiz;c||(c=a.__wiz={});return c[b.toString()]};/*

 Copyright 2024 Google, Inc
 SPDX-License-Identifier: MIT
*/
var lm={};var mm={};function nm(a){var b=document.body,c=Ja(b.getAttribute("jsaction")||"");var d=["u0pjoe"];for(var e=v(d),f=e.next();!f.done;f=e.next()){f=f.value;var g;if(g=c){var h=lm[g];h?g=!!h[f.toString()]:(h=mm[f.toString()],h||(h=new RegExp("(^\\s*"+f+"\\s*:|[\\s;]"+f+"\\s*:)"),mm[f.toString()]=h),g=h.test(g))}else g=!1;g||(c&&!/;$/.test(c)&&(c+=";"),c+=f+":.CLIENT",om(b,c));(g=km(b,f))?g.push(a):b.__wiz[f.toString()]=[a]}return{et:d,hb:a,el:b}}
function om(a,b){a.setAttribute("jsaction",b);"__jsaction"in a&&delete a.__jsaction};function pm(a){X.call(this);this.j=a}A(pm,X);pm.prototype.g=function(a){return qm(this,a)};function rm(a,b){a=Object.prototype.hasOwnProperty.call(a,Aa)&&a[Aa]||(a[Aa]=++Ba);return(b?"__wrapper_":"__protected_")+a+"__"}function qm(a,b){var c=rm(a,!0);b[c]||((b[c]=sm(a,b))[rm(a,!1)]=b);return b[c]}function sm(a,b){function c(){if(a.za())return b.apply(this,arguments);try{return b.apply(this,arguments)}catch(d){tm(a,d)}}c[rm(a,!1)]=b;return c}
function tm(a,b){if(!(b&&typeof b==="object"&&typeof b.message==="string"&&b.message.indexOf("Error in protected function: ")==0||typeof b==="string"&&b.indexOf("Error in protected function: ")==0))throw a.j(b),new um(b);}function vm(a){var b=b||y.window||y.globalThis;"onunhandledrejection"in b&&(b.onunhandledrejection=function(c){tm(a,c&&c.reason?c.reason:Error("uncaught error"))})}
function wm(a,b){var c=y.window||y.globalThis,d=c[b];if(!d)throw Error(b+" not on global?");c[b]=function(e,f){typeof e==="string"&&(e=Ea(Fa,e));e&&(arguments[0]=e=qm(a,e));if(d.apply)return d.apply(this,arguments);var g=e;if(arguments.length>2){var h=Array.prototype.slice.call(arguments,2);g=function(){e.apply(this,h)}}return d(g,f)};c[b][rm(a,!1)]=d}
pm.prototype.L=function(){var a=y.window||y.globalThis;var b=a.setTimeout;b=b[rm(this,!1)]||b;a.setTimeout=b;b=a.setInterval;b=b[rm(this,!1)]||b;a.setInterval=b;pm.Z.L.call(this)};function um(a){Ha.call(this,"Error in protected function: "+(a&&a.message?String(a.message):String(a)),a);(a=a&&a.stack)&&typeof a==="string"&&(this.stack=a)}A(um,Ha);function xm(){};var ym;function zm(){}A(zm,xm);zm.prototype.j=function(){return new XMLHttpRequest};ym=new zm;function Am(a){Bl.call(this);this.headers=new Map;this.V=a||null;this.l=!1;this.g=null;this.M="";this.B=0;this.o=this.K=this.G=this.I=!1;this.A=null;this.O="";this.S=!1}A(Am,Bl);var Bm=/^https?$/i,Cm=["POST","PUT"],Dm=[];r=Am.prototype;r.ib=function(){this.dispose();Za(Dm,this)};
r.send=function(a,b,c,d){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.M+"; newUri="+a);b=b?b.toUpperCase():"GET";this.M=a;this.B=0;this.I=!1;this.l=!0;this.g=this.V?this.V.j():ym.j();this.g.onreadystatechange=di(z(this.Qa,this));try{this.K=!0,this.g.open(b,String(a),!0),this.K=!1}catch(g){Em(this);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get===
"function"){e=v(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("Unknown input type for opt_headers: "+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==g.toLowerCase()});e=y.FormData&&a instanceof y.FormData;!(Xa(Cm,b)>=0)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=v(c);for(d=b.next();!d.done;d=b.next())c=v(d.value),d=c.next().value,c=c.next().value,this.g.setRequestHeader(d,c);this.O&&(this.g.responseType=
this.O);"withCredentials"in this.g&&this.g.withCredentials!==this.S&&(this.g.withCredentials=this.S);try{this.A&&(clearTimeout(this.A),this.A=null),this.G=!0,this.g.send(a),this.G=!1}catch(g){Em(this)}};function Em(a){a.l=!1;a.g&&(a.o=!0,a.g.abort(),a.o=!1);a.B=5;Fm(a);Gm(a)}function Fm(a){a.I||(a.I=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))}
r.abort=function(a){this.g&&this.l&&(this.l=!1,this.o=!0,this.g.abort(),this.o=!1,this.B=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),Gm(this))};r.L=function(){this.g&&(this.l&&(this.l=!1,this.o=!0,this.g.abort(),this.o=!1),Gm(this,!0));Am.Z.L.call(this)};r.Qa=function(){this.za()||(this.K||this.G||this.o?Hm(this):this.Ca())};r.Ca=function(){Hm(this)};
function Hm(a){if(a.l&&typeof va!="undefined")if(a.G&&(a.g?a.g.readyState:0)==4)setTimeout(a.Qa.bind(a),0);else if(a.dispatchEvent("readystatechange"),(a.g?a.g.readyState:0)==4){a.l=!1;try{Ql(a)?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.B=6,Fm(a))}finally{Gm(a)}}}function Gm(a,b){if(a.g){a.A&&(clearTimeout(a.A),a.A=null);var c=a.g;a.g=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){}}}r.isActive=function(){return!!this.g};
function Ql(a){var b=Pl(a);a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}if(!c){if(b=b===0)a=String(a.M).match(Uh)[1]||null,!a&&y.self&&y.self.location&&(a=y.self.location.protocol.slice(0,-1)),b=!Bm.test(a?a.toLowerCase():"");c=b}return c}function Pl(a){try{return(a.g?a.g.readyState:0)>2?a.g.status:-1}catch(b){return-1}}ki(function(a){Am.prototype.Ca=a(Am.prototype.Ca)});function Im(a,b,c){Bl.call(this);this.A=b||null;this.o={};this.B=Jm;this.I=a;if(!c){this.g=null;this.g=new pm(z(this.l,this));wm(this.g,"setTimeout");wm(this.g,"setInterval");a=this.g;b=y.window||y.globalThis;c=["requestAnimationFrame","mozRequestAnimationFrame","webkitAnimationFrame","msRequestAnimationFrame"];for(var d=0;d<c.length;d++){var e=c[d];c[d]in b&&wm(a,e)}a=this.g;ji=!0;b=z(a.g,a);for(c=0;c<gi.length;c++)gi[c](b);hi.push(a)}}A(Im,Bl);
function Km(a,b){Zk.call(this,"c");this.error=a;this.ba=b}A(Km,Zk);function Lm(a,b){return new Im(a,b,void 0)}function Jm(a,b,c,d){if(d instanceof Map){var e={};d=v(d);for(var f=d.next();!f.done;f=d.next()){var g=v(f.value);f=g.next().value;g=g.next().value;e[f]=g}}else e=d;d=new Am;Dm.push(d);d.j.add("ready",d.ib,!0,void 0,void 0);d.send(a,b,c,e)}function Mm(a,b){a.B=b}
Im.prototype.l=function(a,b){a=a.error||a;b=b?Ak(b):{};a instanceof Error&&Ck(b,kb(a));var c=Dh(a);if(this.A)try{this.A(c,b)}catch(x){}var d=c.message.substring(0,1900);if(!(a instanceof Ha)||a.g){var e=c.fileName,f=c.lineNumber;a=c.stack;try{var g=$h(this.I,"script",e,"error",d,"line",f);a:{for(var h in this.o){var l=!1;break a}l=!0}if(!l){l=g;var m=Zh(this.o);g=Wh(l,m)}m={};m.trace=a;if(b)for(var n in b)m["context."+n]=b[n];var w=Zh(m);this.B(g,"POST",w,this.G)}catch(x){}}try{this.dispatchEvent(new Km(c,
b))}catch(x){}};Im.prototype.L=function(){ai(this.g);Im.Z.L.call(this)};function Nm(){this.g=Date.now()}var Om=null;Nm.prototype.set=function(a){this.g=a};Nm.prototype.reset=function(){this.set(Date.now())};Nm.prototype.get=p("g");function Pm(a){this.o=a||"";Om||(Om=new Nm);this.v=Om}Pm.prototype.g=!0;Pm.prototype.j=!0;Pm.prototype.l=!1;function Qm(a){return a<10?"0"+a:String(a)}function Rm(a){Pm.call(this,a)}A(Rm,Pm);
function Sm(a,b){var c=[];c.push(a.o," ");if(a.j){var d=c.push,e=new Date(b.l());d.call(c,"[",Qm(e.getFullYear()-2E3)+Qm(e.getMonth()+1)+Qm(e.getDate())+" "+Qm(e.getHours())+":"+Qm(e.getMinutes())+":"+Qm(e.getSeconds())+"."+Qm(Math.floor(e.getMilliseconds()/10)),"] ")}d=c.push;e=a.v.get();e=(b.l()-e)/1E3;var f=e.toFixed(3),g=0;if(e<1)g=2;else for(;e<100;)g++,e*=10;for(;g-- >0;)f=" "+f;d.call(c,"[",f,"s] ");c.push("[",b.j(),"] ");c.push(b.getMessage());a.l&&(b=b.g(),b!==void 0&&c.push("\n",b instanceof
Error?b.message:String(b)));a.g&&c.push("\n");return c.join("")};function Tm(a){a=a===void 0?new Um:a;Bl.call(this);var b=this;this.S={};this.g=null;this.l={};this.M=new Jl(this);this.jb=a.v;this.V=a.G;this.Ya=a.B;this.gb=a.o;this.Za=a.I;var c=a.j;this.Wa=new hm;this.fb=a.K;this.ca=new Yk;var d=new Am;Vm(this,c);this.G=new Rl(d,c,void 0,void 0);ci(this,this.G);this.o=a.g?a.g:rj(c,"docs-sup")+rj(c,"docs-jepp")+"/jserror";if(d=rj(c,"jobset"))this.o=$h(this.o,"jobset",d);if(d=rj(c,"docs-ci"))this.o=$h(this.o,"id",d);d=rj(c,"docs-pid");oj(c.get("docs-eaotx"))&&d&&
(this.o=$h(this.o,"ouid",d));this.Ja=qj(c,"docs-srmoe")||0;this.bb=oj(c.get("docs-oesf"));this.Ka=qj(c,"docs-srmour")||0;this.eb=oj(c.get("docs-oursf"));d=this.Ka>0&&Math.random()<this.Ka;this.ab=oj(c.get("docs-wesf"));this.La=qj(c,"docs-srmwe")||0;Wm(this);Ti=function(g){return Xm(b,g,"promise rejection")};var e=qj(c,"docs-srmdue")||0;if(e>0&&Math.random()<e){var f=oj(c.get("docs-duesf"));$i=function(g){Xm(b,g,"deferred error",f,"isDeferredUnhandledErrback")}}else $i=k();qj(c,"docs-srmxue");c.get("docs-xduesf");
d&&(d=new pm(function(g){var h={};h=(h.isUnhandledRejection="true",h);b.eb?Ym(b,g,h):b.info(g,h)}),vm(d),ci(this,d));this.K=null;this.La>0&&Math.random()<this.La&&document&&document.body&&(this.K=nm(function(g){var h={};h=(h.isWizError="true",h);g=v(g.data.errors);for(var l=g.next();!l.done;l=g.next())l=l.value.error,b.ab?Ym(b,l,h):b.info(l,h)}));this.O=a.l;this.B=!1;this.I=!0;this.A=!1;this.na=rj(c,"docs-jern");this.Xa=a.A;this.Va=a.C.concat(Object.values(vh))}u(Tm,Bl);
function Wm(a){var b=b===void 0?!1:b;if(Zm){if($m!=null)throw Error('ErrorReporter already installed. at "'+$m.stack+'"');throw Error("ErrorReporter already installed.");}Zm=!0;$m=Error();a.g=Lm(a.o,function(e,f){return an(a,e,f)});var c={};a.Ya&&(c["X-No-Abort"]="1");a.g.G=c;Mm(a.g,function(e,f,g,h){a.I&&a.G.send(e,f,g,h)});if(a.Ja>0&&Math.random()<a.Ja){c={};var d=(c.isWindowOnError="true",c);a.bb?Ch(function(e){Ym(a,e.error instanceof Error?e.error:Error(e.message),d)}):Ch(function(e){a.log(e.error instanceof
Error?e.error:Error(e.message),d)})}a.M.listen(a.g,"c",function(e){e.ba.severity=e.ba["severity-unprefixed"]||e.ba.severity;var f=e.ba.severity;(f=f=="fatal"||f=="postmortem")&&!a.gb&&(!a.jb||(b===void 0?0:b)?a.ca.notify(void 0,e.ba):a.ca.notify(e,e.ba));a.dispatchEvent(new wl(f?"a":"b",e.error,e.ba))})}function Vm(a,b){b=new Tl(b);var c=b.g,d;for(d in c){var e=c[d];e&&(a.l["expflag-"+d]=e.toString())}a.l.experimentIds=b.j.join(",")}
function Ym(a,b,c){a.A=!1;bn(b,"fatal");if(!a.g){if(b instanceof hh)throw b.g;throw Jh(b);}a.g.l(b,cn(a,b,c));if(a.Za){c=cn(a,b,c);c.is_forceFatal=1;var d=b instanceof hh?b.g:b;an(a,d,c);b=Jh(d);a=", context:"+JSON.stringify(cn(a,d,c));b.message+=a;throw b;}}function dn(a,b,c){a.A=!1;bn(b,"warning");a.g&&a.g.l(b,cn(a,b,c))}Tm.prototype.info=function(a,b,c){this.A=c||!1;bn(a,"incident");this.g&&this.g.l(a,cn(this,a,b))};
Tm.prototype.log=function(a,b,c){this.A=!!c;bn(a,"incident");this.g&&this.g.l(a,cn(this,a,b))};
function Xm(a,b,c,d,e){d=d===void 0?!0:d;if(b&&typeof b==="object"&&b.type==="error"){var f=b.error;b=JSON.stringify({error:f&&f.message?f.message:"Missing error cause.",stack:f&&f.stack?f.stack:"Missing error cause.",message:b.message,filename:b.filename,lineno:b.lineno,colno:b.colno,type:b.type});c=Error("Unhandled "+c+" with ErrorEvent: "+b)}else c=typeof b==="string"?Error("Unhandled "+c+" with: "+b):b==null?Error("Unhandled "+c+' with "null/undefined"'):b;b={};e&&(b[e]="true");d?Ia(c):a.info(c,
b)}function en(a,b,c){return function(){a:{var d=sa.apply(0,arguments);if(a.g){try{var e=b.apply(c,d);break a}catch(f){Ym(a,f)}e=void 0}else e=b.apply(c,d)}return e}}function fn(a,b){a.g&&b.then(void 0,function(c){Ym(a,c instanceof Error?c:Error(c))});return b}function cn(a,b,c){b instanceof hh&&(b=b.g);c=c?Ak(c):{};c.severity=kb(b).severity;a.V&&(c.errorGroupId=a.V);return c}
function an(a,b,c){var d=a.B;try{a.ma(b,c)}catch(f){throw d&&!a.O&&(a.I=!1),a.B=!0,c.provideLogDataError=f.message,c.severity||(c.severity="fatal"),Jh(f);}finally{if(c["severity-unprefixed"]=c.severity||"fatal",c.severity=""+c["severity-unprefixed"],!a.Xa)for(var e in c)typeof c[e]==="number"||c[e]instanceof Number||typeof c[e]==="boolean"||c[e]instanceof Boolean||a.Va.includes(e)||e in c&&delete c[e]}}
Tm.prototype.ma=function(a,b){for(var c in this.S)try{b[c]=this.S[c](a)}catch(g){}Ck(b,this.l);if((Th(),0)>0){var d=new Rm,e="";Sh(function(g){e+=Sm(d,g)});b.clientLog=e}c=b.severity||"fatal";this.fb||(c=im(this.Wa,a,c,b));this.na&&(b.reportName=this.na+"_"+c);b.isArrayPrototypeIntact=Ul().toString();try{var f=!!document.getElementById("docs-editor")}catch(g){f=!1}b.isEditorElementAttached=f.toString();b.documentCharacterSet=document.characterSet;f=a.stack||"";if(f.trim().length==0||f=="Not available")b["stacklessError-reportingStack"]=
Ih(Tm.prototype.ma),[a.message].concat(pa(Object.keys(b)),pa(Object.values(b))).some(function(g){return g&&g.includes("<eye3")})||(b.eye3Hint="<eye3-stackless title='Stackless JS Error - "+a.name+"'/>");this.B&&!this.O?(this.I=this.A,c=="fatal"?c="postmortem":c=="incident"&&(c="warningafterdeath")):c=="fatal"&&(this.B=!0);this.A=!1;b.severity=c};
Tm.prototype.L=function(){Zm=!1;if(this.K)for(var a=this.K,b=v(a.et),c=b.next();!c.done;c=b.next()){c=c.value;var d=km(a.el,c);if(d&&(Za(d,a.hb),!d.length)){d=a.el;var e=Ja(d.getAttribute("jsaction")||"");c+=":.CLIENT";e=e.replace(c+";","");e=e.replace(c,"");om(d,e)}}bi(this.M,this.g,this.G);Bl.prototype.L.call(this)};var Zm=!1,$m=null;function Um(){this.G=this.j=void 0;this.o=this.I=this.v=!1;this.g=void 0;this.B=this.l=!1;this.A=!0;this.C=[];this.K=!1}
function bn(a,b){a instanceof hh&&(a=a.g);jb(a,"severity",b)};function gn(a){this.g=null;this.j=a<1;this.l=a<.01}function hn(a,b,c){c=c===void 0?{}:c;a.l&&(c.sampling_samplePercentage=(.01).toString(),a.g.info(b,c))}function jn(a,b,c){c=c===void 0?{}:c;a.j&&(c.sampling_samplePercentage=(1).toString(),dn(a.g,b,c))};function kn(a){this.D=E(a)}u(kn,I);kn.prototype.getMessage=function(){return hd(this,1)};function ln(a){this.D=E(a)}u(ln,I);function mn(a){this.D=E(a)}u(mn,I);function nn(a){this.D=E(a)}u(nn,I);function on(a,b){return ld(a,b)}nn.prototype.Na=function(){return G(this,ln,3)};nn.prototype.xa=function(){return G(this,kn,5)};function pn(a){this.D=E(a)}u(pn,I);function qn(a){var b=new pn;return ld(b,a)}pn.prototype.xa=function(){return G(this,kn,3)};pn.prototype.Na=function(){return G(this,ln,4)};function rn(a){var b=Hi();chrome.runtime.sendMessage(Ec(a),void 0,function(c){return sn(b,function(d){return new pn(d)},c)});return b.promise.catch(function(c){c=Hh(c);jb(c,"offscreenDocumentRequestType",id(a,1).toString());throw c;})}
function sn(a,b,c){var d=chrome.runtime;c!==void 0?(d=b(c),d.xa()?(b=a.reject,c=Error,d=d.xa(),d=jd(d,1),b.call(a,c("Error from Offscreen page:"+d))):a.resolve(d)):a.reject(Error("No response from Offscreen page:"+(d.lastError?d.lastError.message:"without lastError")))};function tn(){return un(chrome.storage.local,["optedInUserOuid"]).then(function(a){return a.optedInUserOuid||null})}function vn(a){return wn({offlineOptedIn:!0}).then(function(){if(a){var b={};return wn((b.optedInUserOuid=a,b))}})}function xn(){return wn({offlineOptedIn:!1}).then(function(){return yn()})}
function zn(){return un(chrome.storage.local,["offlineOptedIn"]).then(function(a){a=a.offlineOptedIn;switch(a){case void 0:return"unknown";case !0:return"opted_in";case !1:return"opted_out";default:throw Error("Cannot handle opt in value "+a);}})}function Fi(){return un(chrome.storage.managed,["allowedDocsOfflineDomains"]).then(function(a){return a&&a.allowedDocsOfflineDomains?a.allowedDocsOfflineDomains:[]})}
function Gi(){return un(chrome.storage.managed,["autoEnabledDocsOfflineDomains"]).then(function(a){return a&&a.autoEnabledDocsOfflineDomains?a.autoEnabledDocsOfflineDomains:[]})}function un(a,b){return new Z(function(c,d){a.get(b,function(e){chrome.runtime.lastError?d(Error(chrome.runtime.lastError)):c(e)})})}function wn(a){return new Z(function(b,c){chrome.storage.local.set(a,function(){chrome.runtime.lastError?c(Error(chrome.runtime.lastError)):b()})})}
function yn(){return new Z(function(a,b){chrome.storage.local.remove("optedInUserOuid",function(){chrome.runtime.lastError?b(Error(chrome.runtime.lastError)):a()})})}function An(){return un(chrome.storage.local,["lastSuccessfulFrameConnectTime"]).then(function(a){return a.lastSuccessfulFrameConnectTime||null})};function Bn(a){this.D=E(a)}u(Bn,I);function Cn(a){this.D=E(a)}u(Cn,I);function Dn(a){this.D=E(a)}u(Dn,I);function En(a){this.D=E(a)}u(En,I);function Fn(a){this.D=E(a)}u(Fn,I);function Gn(a){this.D=E(a)}u(Gn,I);function Hn(a){var b=new Gn;return ld(b,a)}function In(a,b){return dd(a,En,5,b)};function Jn(a,b,c){X.call(this);this.A=null;this.I=a;this.B=b;this.o=c;this.j=Hi();this.l=!1;a=new Dk;Gk(a,"offscreendocument.html");Sk(a,"randomPercentageForSampling",this.o);Sk(a,"sessionId",this.B);this.G={url:a.toString(),reasons:["IFRAME_SCRIPTING"],justification:"Use iframe to access user data under docs.google.com domain"};this.g=new gn(this.o)}u(Jn,X);function Kn(a,b){a.A=b}
function Ln(a){return Mn().then(function(b){return b?rn(Hn(5)).then(function(){return chrome.offscreen.closeDocument()}):Promise.resolve()}).then(function(){Nn(a)})}function On(a,b){return Pn(Qn(a,Rn(a,6,b)))}function Sn(a,b){b=Rn(a,1,b);return Pn(Qn(a,b))}function Tn(a,b){return Mn().then(function(c){b[Un(0)]=c.toString();return c?Promise.resolve():Vn(a)})}
function Vn(a){return chrome.offscreen.createDocument(a.G).catch(function(b){if(b instanceof Error&&b.message.includes("Only a single offscreen document may be created"))hn(a.g,b);else return Promise.reject(b)})}function Wn(a,b){return chrome.offscreen.closeDocument().catch(function(c){c=c instanceof Error?c.message:c.toString();hn(a.g,Error(c),b);b.errorWhenForceCloseOffscreenDoc=c}).then(function(){return Vn(a)})}
function Rn(a,b,c){b=Hn(b);var d=new Bn;c=kd(d,1,c);c=kd(c,2,a.A.toString());a=kd(c,3,a.I);a=kd(a,4,"opted_in");return dd(b,Bn,2,a)}
function Xn(a,b){var c={sendingFrameRequestType:id(b,1)},d=In(Hn(4),b);return Pn(Yn(a,d,c,0)).catch(function(e){if(e instanceof Error){c.offlineFrameConnected_afterFirstError=a.l;if(Zn(e.message))return hn(a.g,e,c),new Promise(function(f){return setTimeout(function(){return f(Yn(a,d,c,1))},2E3)});if("Requests cancelled because user has been opted out"==e.message)return Promise.resolve(new ln)}return Promise.reject(e instanceof Error?e:Error(e))})}
function Yn(a,b,c,d){return a.j.promise.then(function(){return Mn()}).then(function(e){c[Un(d)]=e.toString();return e?Ai():$n(a)}).then(function(){return a.j.promise}).then(function(){return rn(b)}).then(function(e){return e.Na()}).aa(function(e){return ao(e,c)})}function $n(a){Nn(a);return zn().then(function(b){return b=="opted_in"?tn().then(function(c){return Sn(a,c)}):Bi()})}
function Qn(a,b){id(b,1)==6||id(b,1);var c={sendingOffscreenDocumentRequestType:id(b,1).toString()};return Ai(Tn(a,c)).then(function(){return El()}).then(function(){return rn(b)}).aa(function(d){return d instanceof Error&&Zn(d.message)?(hn(a.g,d,c),Ai(Mn()).then(function(e){c[Un(1)]=e.toString()}).then(function(){return Wn(a,c)}).then(function(){return El()}).then(function(){return rn(b)})):Promise.reject(d instanceof Error?d:Error(d))}).aa(function(d){return ao(d,c)})}
function Zn(a){return a.includes("Could not establish connection. Receiving end does not exist.")||a.includes("The message port closed before a response was received.")||a.includes("A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received")}function Mn(){return self.clients.matchAll().then(function(a){return a.some(function(b){return b.url.includes(chrome.runtime.getURL("offscreendocument.html"))})})}
Jn.prototype.L=function(){Ln(this);X.prototype.L.call(this)};function Pn(a){return Promise.resolve(a)}function Nn(a){a.j=Hi();a.l=!1}function Un(a){switch(a){case 0:return"hasDocument_beforeCreatingOffscreenDoc_0";case 1:return"hasDocument_beforeCreatingOffscreenDoc_1";default:throw Error("Cannot get error context key with retryAttempt "+a);}}
function ao(a,b){a=Hh(a);b=v(Object.entries(b));for(var c=b.next();!c.done;c=b.next()){var d=v(c.value);c=d.next().value;d=d.next().value;jb(a,c,d)}throw a;};function bo(a){this.D=E(a)}u(bo,I);function co(a){this.D=E(a)}u(co,I);function eo(a){this.D=E(a)}u(eo,I);function fo(){z(this.l,this);this.g=new Rm;this.g.j=!1;this.g.l=!1;this.j=this.g.g=!1;this.o={}}function go(a){1!=a.j&&(a.j=!0)}fo.prototype.l=function(a){function b(f){if(f){if(f.value>=Nh.value)return"error";if(f.value>=Oh.value)return"warn";if(f.value>=Ph.value)return"log"}return"debug"}if(!this.o[a.j()]){var c=Sm(this.g,a),d=ho;if(d){var e=b(a.o());io(d,e,c,a.g())}}};var ho=y.console;function io(a,b,c,d){if(a[b])a[b](c,d===void 0?"":d);else a.log(c,d===void 0?"":d)};function jo(a){this.o=a.Bb||null;this.l=a.vc||!1;this.g=void 0}A(jo,xm);jo.prototype.j=function(){var a=new ko(this.o,this.l);this.g&&(a.G=this.g);return a};function ko(a,b){Bl.call(this);this.ca=a;this.K=b;this.G=void 0;this.status=this.readyState=0;this.responseType=this.o=this.l=this.statusText="";this.onreadystatechange=null;this.M=new Headers;this.A=null;this.S="GET";this.V="";this.g=!1;this.O=this.B=this.I=null}A(ko,Bl);r=ko.prototype;
r.open=function(a,b){if(this.readyState!=0)throw this.abort(),Error("Error reopening a connection");this.S=a;this.V=b;this.readyState=1;lo(this)};r.send=function(a){if(this.readyState!=1)throw this.abort(),Error("need to call open() first. ");this.g=!0;var b={headers:this.M,method:this.S,credentials:this.G,cache:void 0};a&&(b.body=a);(this.ca||y).fetch(new Request(this.V,b)).then(this.pb.bind(this),this.ra.bind(this))};
r.abort=function(){this.l=this.o="";this.M=new Headers;this.status=0;this.B&&this.B.cancel("Request was aborted.").catch(k());this.readyState>=1&&this.g&&this.readyState!=4&&(this.g=!1,mo(this));this.readyState=0};
r.pb=function(a){if(this.g&&(this.I=a,this.A||(this.status=this.I.status,this.statusText=this.I.statusText,this.A=a.headers,this.readyState=2,lo(this)),this.g&&(this.readyState=3,lo(this),this.g)))if(this.responseType==="arraybuffer")a.arrayBuffer().then(this.nb.bind(this),this.ra.bind(this));else if(typeof y.ReadableStream!=="undefined"&&"body"in a){this.B=a.body.getReader();if(this.K){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.l=
[]}else this.l=this.o="",this.O=new TextDecoder;no(this)}else a.text().then(this.ob.bind(this),this.ra.bind(this))};function no(a){a.B.read().then(a.mb.bind(a)).catch(a.ra.bind(a))}r.mb=function(a){if(this.g){if(this.K&&a.value)this.l.push(a.value);else if(!this.K){var b=a.value?a.value:new Uint8Array(0);if(b=this.O.decode(b,{stream:!a.done}))this.l=this.o+=b}a.done?mo(this):lo(this);this.readyState==3&&no(this)}};r.ob=function(a){this.g&&(this.l=this.o=a,mo(this))};
r.nb=function(a){this.g&&(this.l=a,mo(this))};r.ra=function(){this.g&&mo(this)};function mo(a){a.readyState=4;a.I=null;a.B=null;a.O=null;lo(a)}r.setRequestHeader=function(a,b){this.M.append(a,b)};r.getResponseHeader=function(a){return this.A?this.A.get(a.toLowerCase())||"":""};r.getAllResponseHeaders=function(){if(!this.A)return"";for(var a=[],b=this.A.entries(),c=b.next();!c.done;)c=c.value,a.push(c[0]+": "+c[1]),c=b.next();return a.join("\r\n")};
function lo(a){a.onreadystatechange&&a.onreadystatechange.call(a)}Object.defineProperty(ko.prototype,"withCredentials",{get:function(){return this.G==="include"},set:function(a){this.G=a?"include":"same-origin"}});var oo=new jo({Bb:self});oo.g="same-origin";ym=oo;
function po(){X.call(this);var a=this;this.G=kh();this.g=this.A=null;this.M=!1;this.U=new fo;go(this.U);this.K=Hi();chrome.alarms.onAlarm.addListener(function(b){return a.K.promise.then(function(){return fn(a.g,en(a.g,a.sb,a)(b))})});chrome.runtime.onMessageExternal.addListener(function(b,c,d){return qo(a,b,d)});chrome.runtime.onMessage.addListener(this.tb.bind(this));this.B=new Jl(this);ci(this,this.B);this.B.listen(y,"message",this.ub);this.o=Math.random()*100;this.I=this.o<1;this.l=new gn(this.o);
this.j=new Jn(ro(),this.G,this.o);chrome.runtime.onConnectExternal.addListener(k());Dl(this.yb,252E5,this)}u(po,X);r=po.prototype;r.load=function(){var a=this;this.A="docs.google.com";return wn({docsDomain:this.A}).then(function(){a.g=so(a);a.K.resolve();ci(a,a.g);a.l.g=a.g;a.j.g.g=a.g;Kn(a.j,to(a));ci(a,a.j);var b=en(a.g,a.qb,a),c=fn(a.g,Ai().then(function(){return b()}));return Ai(c)}).aa(function(b){Hh(b)})};
function uo(a,b,c){return Sk(Sk(Gk(to(a),"/offline/extension/report"),"v",c),"optin",b).toString()}r.yb=function(){chrome.alarms.create("open",{delayInMinutes:1});hn(this.l,Error("Called unsafeClose_"))};function vo(a){return new Z(function(b){chrome.alarms.get("heartbeat",function(c){c||(chrome.alarms.create("heartbeat",{periodInMinutes:5}),wo(a,"heartbeat"));b()})})}function xo(){return new Z(function(a){chrome.alarms.clear("heartbeat",function(){a()})})}
r.qb=function(){var a=this;return An().then(function(b){a.g.l.lastSuccessfulFrameConnectTime=(b==null?void 0:b.toString())||"null"}).then(function(){return zn()}).then(function(b){var c=ro();a.g.l.extensionVersion=c;a.g.l.optInStatus=String(b);yo(a,String(b),c);switch(b){case "unknown":break;case "opted_in":return tn().then(function(d){return Sn(a.j,d)});case "opted_out":break;default:throw Error("Could not handle opt in status "+b);}})};
function yo(a,b,c){a.I&&(b=uo(a,b,c),y.fetch(new Request(b,{method:"post",mode:"cors"})).then(k()).catch(function(d){dn(a.g,Hh(d))}))}r.ub=function(a){var b=a.g;b&&b.data&&b.ports&&b.ports.length?(a=new eo(b.data),zo(this,a,b.ports.length>1?b.ports[1]:void 0).then(function(c){b.ports[0].postMessage(Ec(c))})):jn(this.l,Error("Dropped invalid event."),{event:String(a)})};
function qo(a,b,c){var d=new eo(b);zo(a,d).then(function(e){c(Ec(e))}).aa(function(e){if(e instanceof Error&&e.message=="Attempting to use a disconnected port object")jn(a.l,Error("Failed to reply to request because listen port was disconnected."),{requestType:oc(F(d,1,void 0,Pc))});else throw e;});return!0}
r.tb=function(a,b,c){var d=this;a=new Gn(a);switch(oc(F(a,1,void 0,Pc))){case 3:var e=G(a,Cn,4);a=uc(F(e,1))!=null?hd(e,1):null;var f=jd(e,2);vn(a).then(function(){return wn({lastSuccessfulFrameConnectTime:parseInt(f,10)})}).then(function(){var h=d.j;h.l=!0;h.j.resolve()}).then(function(){var h=qn(3);c(Ec(h))});break;case 7:var g=qn(7);(a=(e=G(a,Fn,6))==null?void 0:hd(e,1))?Ao(this,a).then(function(){return c(Ec(g))}):Bo(this).then(function(){return c(Ec(g))});break;default:throw Error("Unsupported OffscreenDocumentRequestType.");
}return!0};function zo(a,b,c){return Ai().then(a.wb.bind(a,b,c)).aa(function(d){d=d instanceof Error?d:Error(d);var e=new nn,f=new kn;dd(e,kn,5,f);kd(f,1,d.message);return e})}
r.wb=function(a){var b=this,c=on(new nn,oc(F(a,1)));switch(oc(F(a,1,void 0,Pc))){case 1:return(a=(a=G(a,Cn,7))?jd(a,1):null)||hn(this.l,Error("Scheduler frame connect request sent without an ouid.")),vn(a).then(function(){return wn({lastSuccessfulFrameConnectTime:Date.now()})}).then(function(){var e=b.j;e.l=!0;e.j.resolve()}).then(function(){return c});case 2:var d=(a=G(a,bo,8))?jd(a,1):null;return vn(d).then(function(){return d?d:tn()}).then(function(e){return On(b.j,e).then(function(){return vo(b)})}).then(function(){return c});
case 3:return(a=G(a,Fn,3))&&hd(a,1)?(a=hd(a,1),Ao(this,a).then(function(){return c})):Bo(this).then(function(){return c});case 5:return Co(G(a,co,5)).then(function(e){dd(c,mn,4,e);return c});case 4:return a=G(a,En,4),Ai(Xn(this.j,a)).then(function(e){dd(c,ln,3,e);return c})}throw Error("Dropped unknown message "+a);};
function Co(a){var b=jd(a,1);return Ei().then(function(c){var d=c[0],e=c[1];c=new mn;var f=Xa(d,b)>=0;d=Xa(e,b)>=0;e=f||d;e=e==null?e:kc(e);Rc(c,1,e);Rc(c,2,d==null?d:kc(d));return c})}function Ao(a,b){return a.M?Ai(Ln(a.j)):(hn(a.l,Error("Extension frame connected with the wrong OUID.")),a.M=!0,Ai(Sn(a.j,b)))}function Bo(a){return xn().then(function(){return xo()}).then(function(){return Ln(a.j)})}r.sb=function(a){return wo(this,a.name)};
function wo(a,b){var c=new En;c=ld(c,0);var d=new Dn;b=kd(d,1,b);dd(c,Dn,2,b);return Ai(Xn(a.j,c))}function so(a){var b=Gk(to(a),"/offline/jserror").toString(),c=a.I;a=String(a.G);var d=new Um;d.v=!1;d.o=!0;d.g=b;d.l=!0;b=lj();d.j=b;b=new Tm(d);b.l.sessionTypeName="offline-event-page";b.l.reportsNonFatalErrors=String(c);b.l.sid=a;return b}function to(a){return Ek(new Dk("//"+a.A),"https")}function ro(){var a=chrome.runtime.getManifest();return a.version?a.version:"unknown"};self.window=self;(new po).load();

#!/usr/bin/env python3
"""
Debug script for YouTube upload process
"""

import os
import sys
import time
import json
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def setup_driver():
    """Setup Chrome driver with profile"""
    chrome_options = Options()

    # Use the existing profile
    profile_path = os.path.join(os.getcwd(), "browser_profiles", "ytfinance_bot")
    chrome_options.add_argument(f"--user-data-dir={profile_path}")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--remote-debugging-port=9222")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.maximize_window()
        return driver
    except Exception as e:
        print(f"❌ Error setting up Chrome driver: {e}")
        print("🔄 Trying without profile...")

        # Fallback: try without profile
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.maximize_window()
        return driver

def debug_upload_page(driver):
    """Debug the YouTube upload page"""
    print("🔍 Debugging YouTube upload page...")

    # First try YouTube Studio main page
    print("📍 Navigating to YouTube Studio main page...")
    driver.get("https://studio.youtube.com")
    time.sleep(5)

    current_url = driver.current_url
    page_title = driver.title
    print(f"📍 Current URL: {current_url}")
    print(f"📍 Page title: {page_title}")

    # Check if logged in
    if "accounts.google.com" in current_url or "signin" in current_url.lower():
        print("❌ Not logged in! Please log in manually.")
        print("🔄 Trying to navigate to login page...")
        driver.get("https://accounts.google.com/signin")
        time.sleep(10)
        print("⏳ Please log in manually and then press Enter...")
        input("Press Enter after logging in...")

        # Try YouTube Studio again
        driver.get("https://studio.youtube.com")
        time.sleep(5)
        current_url = driver.current_url
        if "accounts.google.com" in current_url or "signin" in current_url.lower():
            print("❌ Still not logged in!")
            return False

    print("✅ Successfully accessed YouTube Studio")

    # Navigate to upload page - try different URLs
    upload_urls = [
        "https://studio.youtube.com/channel/upload",
        "https://www.youtube.com/upload",
        "https://studio.youtube.com/upload"
    ]

    upload_success = False
    for upload_url in upload_urls:
        print(f"📍 Trying upload URL: {upload_url}")
        driver.get(upload_url)
        time.sleep(8)

        current_url = driver.current_url
        page_title = driver.title
        print(f"📍 Result URL: {current_url}")
        print(f"📍 Result title: {page_title}")

        # Check if we're on an upload page
        if "upload" in current_url.lower() or "upload" in page_title.lower():
            print("✅ Successfully reached upload page!")
            upload_success = True
            break
        else:
            print(f"❌ Not upload page, trying next URL...")

    if not upload_success:
        print("❌ Could not reach upload page with any URL")
        # Try clicking upload button from dashboard
        print("🔄 Trying to find upload button on current page...")
        try:
            upload_buttons = driver.find_elements(By.CSS_SELECTOR,
                "[aria-label*='upload' i], [aria-label*='create' i], button:contains('CREATE'), button:contains('Upload')")
            if upload_buttons:
                print(f"✅ Found {len(upload_buttons)} potential upload buttons")
                upload_buttons[0].click()
                time.sleep(5)
                current_url = driver.current_url
                print(f"📍 After clicking upload button: {current_url}")
            else:
                print("❌ No upload buttons found")
                return False
        except Exception as e:
            print(f"❌ Error clicking upload button: {e}")
            return False
    
    # Look for file input
    print("🔍 Looking for file input...")
    try:
        file_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='file']")
        print(f"📁 Found {len(file_inputs)} file input elements")
        for i, inp in enumerate(file_inputs):
            accept_attr = inp.get_attribute("accept") or "No accept attribute"
            print(f"   File input {i+1}: accept={accept_attr}")
    except Exception as e:
        print(f"❌ Error finding file inputs: {e}")
    
    # Upload a test file
    video_path = os.path.join("temp", "assembled_video.mp4")
    if os.path.exists(video_path):
        abs_video_path = os.path.abspath(video_path)
        print(f"📹 Found test video: {abs_video_path}")
        print(f"📏 File size: {os.path.getsize(abs_video_path) / (1024*1024):.1f} MB")

        try:
            print("🔍 Looking for file input element...")
            file_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='file']")
            if not file_inputs:
                print("❌ No file input found!")
                return False

            file_input = file_inputs[0]
            print(f"✅ Found file input element")

            print("📤 Uploading file...")
            file_input.send_keys(abs_video_path)
            print("✅ File path sent to input")

            # Wait for upload to process
            print("⏳ Waiting for upload to process...")
            time.sleep(15)  # Increased wait time
            
            # Check for metadata form
            print("🔍 Looking for metadata form elements...")
            
            # Log all input/editable elements
            all_inputs = driver.find_elements(By.CSS_SELECTOR, "input, textarea, div[contenteditable='true']")
            print(f"📝 Found {len(all_inputs)} input/editable elements:")
            
            for i, elem in enumerate(all_inputs[:20]):  # Show first 20
                try:
                    tag_name = elem.tag_name
                    aria_label = elem.get_attribute("aria-label") or "No aria-label"
                    class_name = elem.get_attribute("class") or "No class"
                    id_attr = elem.get_attribute("id") or "No id"
                    placeholder = elem.get_attribute("placeholder") or "No placeholder"
                    
                    print(f"   Element {i+1}: {tag_name}")
                    print(f"      aria-label: {aria_label}")
                    print(f"      class: {class_name}")
                    print(f"      id: {id_attr}")
                    print(f"      placeholder: {placeholder}")
                    print("      ---")
                except Exception as e:
                    print(f"   Element {i+1}: Error getting attributes - {e}")
            
            # Try to find title field specifically
            print("\n🎯 Looking specifically for title field...")
            title_selectors = [
                "div[aria-label='Add a title that describes your video (type @ to mention a channel)']",
                "div[aria-label*='title' i]",
                "div[contenteditable='true'][aria-label*='title' i]",
                "#textbox[aria-label*='title' i]",
                "ytcp-social-suggestion-input div[contenteditable='true']",
                "div[slot='textbox']",
                "#textbox",
                "div[contenteditable='true']",
                "input[aria-label*='title' i]",
                "textarea[aria-label*='title' i]"
            ]
            
            for i, selector in enumerate(title_selectors):
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"✅ Selector {i+1} found {len(elements)} elements: {selector}")
                        for j, elem in enumerate(elements):
                            try:
                                aria_label = elem.get_attribute("aria-label") or "No aria-label"
                                print(f"   Element {j+1}: {aria_label}")
                            except:
                                pass
                    else:
                        print(f"❌ Selector {i+1} found no elements: {selector}")
                except Exception as e:
                    print(f"❌ Selector {i+1} error: {e}")
            
            # Take a screenshot for manual inspection
            screenshot_path = f"debug_upload_page_{int(time.time())}.png"
            driver.save_screenshot(screenshot_path)
            print(f"📸 Screenshot saved: {screenshot_path}")
            
            # Save page source
            source_path = f"debug_upload_page_{int(time.time())}.html"
            with open(source_path, 'w', encoding='utf-8') as f:
                f.write(driver.page_source)
            print(f"📄 Page source saved: {source_path}")
            
        except Exception as e:
            print(f"❌ Error during upload test: {e}")
            return False
    else:
        print(f"❌ Test video not found: {video_path}")
        return False
    
    return True

def main():
    """Main debug function"""
    print("🚀 Starting YouTube upload debug...")
    
    driver = None
    try:
        driver = setup_driver()
        success = debug_upload_page(driver)
        
        if success:
            print("✅ Debug completed successfully")
        else:
            print("❌ Debug failed")
            
        # Keep browser open for manual inspection
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Debug script error: {e}")
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    main()

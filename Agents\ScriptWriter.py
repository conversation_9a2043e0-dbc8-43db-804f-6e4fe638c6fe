import re
import xml.etree.ElementTree as ET
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from Helpers.LLMRequest import LLMRequest, LLMConfig, ModelType


@dataclass
class Scene:
    """Represents a single scene in the script"""
    text: str
    image_prompt: str
    
    def __str__(self) -> str:
        return f"Scene(text='{self.text[:50]}...', image='{self.image_prompt[:50]}...')"


@dataclass
class Script:
    """Represents a complete script with multiple scenes"""
    scenes: List[Scene]
    
    def __len__(self) -> int:
        return len(self.scenes)
    
    def __iter__(self):
        return iter(self.scenes)
    
    def to_dict(self) -> Dict:
        """Convert script to dictionary format"""
        return {
            "scenes": [
                {
                    "text": scene.text,
                    "image_prompt": scene.image_prompt
                }
                for scene in self.scenes
            ]
        }


class ScriptParserError(Exception):
    """Custom exception for script parsing errors"""
    pass


class ScriptWriter:
    """
    Agent for generating and parsing video scripts using LLM responses
    
    Features:
    - Generate scripts from topics using LLM
    - Parse XML-formatted script responses
    - Extract scenes with text and image prompts
    - Error handling for malformed XML
    """
    
    def __init__(self, api_key: Optional[str] = None, model: ModelType = ModelType.GEMINI_2_5_FLASH):
        """
        Initialize the ScriptWriter agent
        
        Args:
            api_key: Gemini API key (optional, will use environment variable if not provided)
            model: Model type to use for generation
        """
        self.llm_config = LLMConfig(
            model=model,
            temperature=0.7,
            system_instruction="""You are a professional script writer for engaging video content. 
            Create compelling scripts with visual descriptions for each scene.
            
            Always respond with the following XML format:
            <script>
            <scene>
            <text>The narrative text for this scene</text>
            <image>Detailed image prompt describing what should be shown visually</image>
            </scene>
            <scene>
            <text>The narrative text for this scene</text>
            <image>Detailed image prompt describing what should be shown visually</image>
            </scene>
            </script>
            
            Make each scene engaging and ensure image prompts are detailed and specific."""
        )
        
        self.llm = LLMRequest(api_key=api_key, config=self.llm_config)
    
    def generate_script(self, topic: str, num_scenes: int = 5) -> Script:
        """
        Generate a script for the given topic
        
        Args:
            topic: The topic/subject for the script
            num_scenes: Number of scenes to generate (default: 5)
            
        Returns:
            Parsed Script object
            
        Raises:
            ScriptParserError: If the LLM response cannot be parsed
        """
        prompt = f"""Create an engaging {num_scenes}-scene script about: {topic}

        Each scene should:
        1. Have compelling narrative text (1-2 sentences)
        2. Include a detailed image prompt for visual content
        3. Flow logically to the next scene
        4. Be suitable for a short video format

        Topic: {topic}
        Number of scenes: {num_scenes}

        Remember to format your response as XML with the exact structure specified in your instructions."""
        
        try:
            response = self.llm.generate_response(prompt, add_to_history=False)
            return self.parse_script_response(response)
        except Exception as e:
            raise ScriptParserError(f"Failed to generate script: {str(e)}")
    
    def parse_script_response(self, response: str) -> Script:
        """
        Parse the LLM response containing XML script format
        
        Args:
            response: Raw LLM response containing XML script
            
        Returns:
            Parsed Script object
            
        Raises:
            ScriptParserError: If the response cannot be parsed
        """
        # Clean the response text
        cleaned_response = self._clean_response(response)
        
        # Extract XML content
        xml_content = self._extract_xml_content(cleaned_response)
        
        # Parse XML and extract scenes
        scenes = self._parse_xml_scenes(xml_content)
        
        if not scenes:
            raise ScriptParserError("No scenes found in the response")
        
        return Script(scenes=scenes)
    
    def _clean_response(self, response: str) -> str:
        """
        Clean the response text by removing markdown formatting and extra whitespace
        
        Args:
            response: Raw response text
            
        Returns:
            Cleaned response text
        """
        # Remove markdown code blocks
        response = re.sub(r'```[a-zA-Z]*\n?', '', response)
        response = re.sub(r'```', '', response)
        
        # Remove extra whitespace while preserving line breaks
        response = re.sub(r'[ \t]+', ' ', response)
        response = re.sub(r'\n\s*\n', '\n', response)
        
        return response.strip()
    
    def _extract_xml_content(self, response: str) -> str:
        """
        Extract XML content from the response
        
        Args:
            response: Cleaned response text
            
        Returns:
            XML content string
            
        Raises:
            ScriptParserError: If no valid XML is found
        """
        # Try to find script tags (handle both 'script' and 'sccene' typos)
        script_patterns = [
            r'<script>(.*?)</script>',
            r'<script[^>]*>(.*?)</script>',
        ]
        
        for pattern in script_patterns:
            match = re.search(pattern, response, re.DOTALL | re.IGNORECASE)
            if match:
                return f"<script>{match.group(1)}</script>"
        
        # If no script tags found, check if the entire response is XML-like
        if '<scene>' in response.lower() or '<sccene>' in response.lower():
            # Wrap in script tags if missing
            if not response.strip().startswith('<script'):
                return f"<script>{response}</script>"
            return response
        
        raise ScriptParserError("No valid XML script content found in response")
    
    def _parse_xml_scenes(self, xml_content: str) -> List[Scene]:
        """
        Parse XML content and extract scenes
        
        Args:
            xml_content: XML string containing script
            
        Returns:
            List of Scene objects
            
        Raises:
            ScriptParserError: If XML parsing fails
        """
        scenes = []
        
        try:
            # Handle common typo 'sccene' instead of 'scene'
            xml_content = re.sub(r'</?sccene>', lambda m: m.group(0).replace('sccene', 'scene'), xml_content)
            
            # Parse XML
            root = ET.fromstring(xml_content)
            
            # Find all scene elements
            scene_elements = root.findall('.//scene')
            
            for scene_elem in scene_elements:
                text_elem = scene_elem.find('text')
                image_elem = scene_elem.find('image')
                
                if text_elem is not None and image_elem is not None:
                    scene = Scene(
                        text=text_elem.text.strip() if text_elem.text else "",
                        image_prompt=image_elem.text.strip() if image_elem.text else ""
                    )
                    
                    # Only add scenes that have both text and image content
                    if scene.text and scene.image_prompt:
                        scenes.append(scene)
        
        except ET.ParseError as e:
            # Try alternative parsing methods for malformed XML
            scenes = self._parse_xml_fallback(xml_content)
            if not scenes:
                raise ScriptParserError(f"XML parsing failed: {str(e)}")
        
        except Exception as e:
            raise ScriptParserError(f"Unexpected error during XML parsing: {str(e)}")
        
        return scenes
    
    def _parse_xml_fallback(self, xml_content: str) -> List[Scene]:
        """
        Fallback method to parse XML using regex when ET fails
        
        Args:
            xml_content: XML string that failed standard parsing
            
        Returns:
            List of Scene objects
        """
        scenes = []
        
        # Use regex to extract scenes when XML parsing fails
        scene_pattern = r'<scene[^>]*>(.*?)</scene>'
        scene_matches = re.findall(scene_pattern, xml_content, re.DOTALL | re.IGNORECASE)
        
        for scene_content in scene_matches:
            # Extract text and image from scene content
            text_match = re.search(r'<text[^>]*>(.*?)</text>', scene_content, re.DOTALL | re.IGNORECASE)
            image_match = re.search(r'<image[^>]*>(.*?)</image>', scene_content, re.DOTALL | re.IGNORECASE)
            
            if text_match and image_match:
                text = text_match.group(1).strip()
                image_prompt = image_match.group(1).strip()
                
                if text and image_prompt:
                    scenes.append(Scene(text=text, image_prompt=image_prompt))
        
        return scenes
    
    def validate_script(self, script: Script) -> Tuple[bool, List[str]]:
        """
        Validate a script and return validation results
        
        Args:
            script: Script object to validate
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []
        
        if len(script.scenes) == 0:
            issues.append("Script has no scenes")
        
        for i, scene in enumerate(script.scenes):
            if not scene.text.strip():
                issues.append(f"Scene {i+1} has empty text")
            
            if not scene.image_prompt.strip():
                issues.append(f"Scene {i+1} has empty image prompt")
            
            if len(scene.text.split()) < 3:
                issues.append(f"Scene {i+1} text is too short (less than 3 words)")
            
            if len(scene.image_prompt.split()) < 3:
                issues.append(f"Scene {i+1} image prompt is too short (less than 3 words)")
        
        return len(issues) == 0, issues


# Example usage and testing
if __name__ == "__main__":
    import os
    
    # Test the ScriptWriter with example responses
    writer = ScriptWriter()
    
    # Test parsing of properly formatted response
    test_response = """
    <script>
    <scene>
    <text>Welcome to our exploration of renewable energy technologies that are reshaping our world.</text>
    <image>A sweeping aerial view of a modern solar farm with thousands of gleaming solar panels arranged in perfect rows, set against a bright blue sky with white clouds</image>
    </scene>
    <scene>
    <text>Solar power has become the fastest-growing energy source globally, with costs dropping by over 80% in the past decade.</text>
    <image>Close-up of solar panels with sunlight reflecting off their surface, showing the intricate grid pattern of photovoltaic cells</image>
    </scene>
    </script>
    """
    
    try:
        script = writer.parse_script_response(test_response)
        print(f"Successfully parsed script with {len(script)} scenes:")
        for i, scene in enumerate(script.scenes, 1):
            print(f"\nScene {i}:")
            print(f"Text: {scene.text}")
            print(f"Image: {scene.image_prompt}")
        
        # Validate the script
        is_valid, issues = writer.validate_script(script)
        print(f"\nScript validation: {'PASSED' if is_valid else 'FAILED'}")
        if issues:
            for issue in issues:
                print(f"- {issue}")
    
    except ScriptParserError as e:
        print(f"Parsing error: {e}")

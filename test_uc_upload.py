#!/usr/bin/env python3
"""
Test script using undetected_chromedriver like the YouTubeUploader
"""

import os
import sys
import time
import json
from pathlib import Path

try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def setup_uc_driver():
    """Setup undetected Chrome driver with profile"""
    try:
        options = uc.ChromeOptions()
        
        # Use the existing profile
        profile_path = os.path.join(os.getcwd(), "browser_profiles")
        options.add_argument(f"--user-data-dir={profile_path}")
        options.add_argument("--profile-directory=ytfinance_bot")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        
        print("🚀 Initializing undetected Chrome driver...")
        driver = uc.Chrome(options=options)
        driver.maximize_window()
        
        return driver
    except Exception as e:
        print(f"❌ Error setting up undetected Chrome driver: {e}")
        return None

def test_upload_with_uc():
    """Test upload process with undetected chrome"""
    driver = None
    try:
        driver = setup_uc_driver()
        if not driver:
            return False
            
        wait = WebDriverWait(driver, 30)
        
        print("📍 Navigating to YouTube upload page...")
        driver.get("https://www.youtube.com/upload")
        time.sleep(8)
        
        current_url = driver.current_url
        page_title = driver.title
        print(f"📍 Current URL: {current_url}")
        print(f"📍 Page title: {page_title}")
        
        # Check if logged in
        if "accounts.google.com" in current_url or "signin" in current_url.lower():
            print("❌ Not logged in!")
            return False
        
        # Look for file input
        print("🔍 Looking for file input...")
        try:
            file_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='file']")
            print(f"📁 Found {len(file_inputs)} file input elements")
            
            if not file_inputs:
                print("❌ No file input found!")
                return False
                
        except Exception as e:
            print(f"❌ Error finding file inputs: {e}")
            return False
        
        # Upload test video
        video_path = os.path.join("temp", "assembled_video.mp4")
        if os.path.exists(video_path):
            abs_video_path = os.path.abspath(video_path)
            print(f"📹 Uploading: {abs_video_path}")
            
            try:
                file_input = file_inputs[0]
                file_input.send_keys(abs_video_path)
                print("✅ File uploaded")
                
                # Wait for upload processing
                print("⏳ Waiting for upload processing...")
                time.sleep(15)
                
                # Look for title field
                print("🔍 Looking for title field...")
                title_selectors = [
                    "div[aria-label='Add a title that describes your video (type @ to mention a channel)']",
                    "div[aria-label*='title' i]",
                    "#textbox[aria-label*='title' i]"
                ]
                
                title_element = None
                for i, selector in enumerate(title_selectors):
                    try:
                        print(f"   Trying selector {i+1}: {selector}")
                        title_element = wait.until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                        print(f"✅ Found title field with selector {i+1}")
                        break
                    except (NoSuchElementException, TimeoutException) as e:
                        print(f"   Selector {i+1} failed: {e}")
                        continue
                
                if title_element:
                    print("🎯 Testing title input...")
                    test_title = "Test Video Title"
                    
                    title_element.click()
                    time.sleep(1)
                    title_element.clear()
                    title_element.send_keys(test_title)
                    
                    print(f"✅ Title set to: {test_title}")
                    
                    # Verify
                    time.sleep(1)
                    current_text = title_element.text or title_element.get_attribute("textContent") or ""
                    print(f"📝 Current title text: {current_text}")
                    
                    if test_title.lower() in current_text.lower():
                        print("✅ Title verification successful!")
                        return True
                    else:
                        print("❌ Title verification failed!")
                        return False
                else:
                    print("❌ Could not find title field")
                    
                    # Debug: log all available elements
                    all_inputs = driver.find_elements(By.CSS_SELECTOR, "input, textarea, div[contenteditable='true']")
                    print(f"📝 Found {len(all_inputs)} input/editable elements:")
                    for i, elem in enumerate(all_inputs[:10]):
                        try:
                            aria_label = elem.get_attribute("aria-label") or "No aria-label"
                            tag_name = elem.tag_name
                            print(f"   Element {i+1}: {tag_name} - {aria_label}")
                        except:
                            pass
                    
                    return False
                    
            except Exception as e:
                print(f"❌ Error during upload: {e}")
                return False
        else:
            print(f"❌ Video file not found: {video_path}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    finally:
        if driver:
            print("⏳ Keeping browser open for inspection...")
            input("Press Enter to close browser...")
            driver.quit()

def main():
    """Main test function"""
    print("🧪 Testing YouTube upload with undetected Chrome...")
    
    success = test_upload_with_uc()
    
    if success:
        print("✅ Test completed successfully!")
    else:
        print("❌ Test failed!")

if __name__ == "__main__":
    main()
